NODE_ENV=production
PORT=8080
SERVER_ORIGIN=https://aida-prod-447812.nw.r.appspot.com
GCP_PROJECT_ID=aida-prod-447812
GCP_LOCATION=us-central1

# db
DB_HOST=/cloudsql/aida-prod-447812:europe-west2:aida-db-prod
DB_USERNAME=postgres
DB_DATABASE=aida

GCS_BUCKET=aida-storages-prod

DEFAULT_TIMEZONE=Europe/London

# Recall
RECALL_API_KEY=cd52341f1ca1f0f3c2860861edffec4b144ee25d
RECALL_API_BASE_URL_V1=https://eu-central-1.recall.ai/api/v1
RECALL_API_BASE_URL_V2=https://eu-central-1.recall.ai/api/v2
RECALL_SYSTEM_CALENDAR_EMAIL=<EMAIL>

GCP_PUBSUB_TOPIC_NAME=projects/aida-prod-447812/topics/cloudlab-resouce
GCP_PUBSUB_SUBSCRIPTION_NAME=projects/aida-prod-447812/subscriptions/cloudlab-resouce-sub
GCP_PUBSUB_TRANSCRIBER_TOPIC_NAME=projects/aida-prod-447812/topics/transcoder-notify
GCP_PUBSUB_TRANSCRIBER_SUBSCRIPTION_NAME=projects/aida-prod-447812/subscriptions/transcoder-notify-sub

AIDA_API_KEY=g7jWzK215p9RsT6b8yqqaDk4f1mXvAHEcZ3BnL8o96Iwd57rGjxF2aUeYKPzNlQ

#sendgrid
SENDGRID_FROM_EMAIL='<EMAIL>'
SENDGRID_FROM_NAME='Aida @ Beings'

# gemini
GEMINI_MODEL=gemini-2.0-flash

# aida client
AIDA_ENDPOINT=https://app.beings.com

# jwt
JWT_SECRET=aIdApRoD-secret

# Posthog
POSTHOG_KEY=phc_7RV4sgO0Hzw6NrLZeajusaqP0NSJ3algsCJ6whs7lOb
POSTHOG_HOST=https://us.i.posthog.com

WEB_APP_URL=https://aida.beings.com
