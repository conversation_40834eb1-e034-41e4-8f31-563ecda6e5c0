NODE_ENV=development
PORT=8080
SERVER_ORIGIN=https://aida-22a9a.nw.r.appspot.com
GCP_PROJECT_ID=aida-22a9a
GCP_LOCATION=us-central1

# db
DB_HOST=/cloudsql/aida-22a9a:europe-west2:aida-db-dev
DB_USERNAME=postgres
DB_DATABASE=aida

GCS_BUCKET=aida-storages-dev

DEFAULT_TIMEZONE=Europe/London

# Recall
RECALL_API_KEY=e86da6ec7768c9e00044e7095050b8fb3f4151be
RECALL_API_BASE_URL_V1=https://eu-central-1.recall.ai/api/v1
RECALL_API_BASE_URL_V2=https://eu-central-1.recall.ai/api/v2
RECALL_SYSTEM_CALENDAR_EMAIL=<EMAIL>

GCP_PUBSUB_TOPIC_NAME=projects/aida-22a9a/topics/cloudlab-resouce
GCP_PUBSUB_SUBSCRIPTION_NAME=projects/aida-22a9a/subscriptions/cloudlab-resouce-sub
GCP_PUBSUB_TRANSCRIBER_TOPIC_NAME=projects/aida-22a9a/topics/transcoder-notify
GCP_PUBSUB_TRANSCRIBER_SUBSCRIPTION_NAME=projects/aida-22a9a/subscriptions/transcoder-notify-sub
AIDA_API_KEY=8IXtyR898g8OplVd9frruCzcf7sqXQINeH5GaUp7l28Knd03aFmpF3bXuhLTFcFX

AIDA_ENDPOINT=https://dev-aida.beings.com

#sendgrid
SENDGRID_FROM_EMAIL='<EMAIL>'
SENDGRID_FROM_NAME='Aida @ Beings'

# gemini
GEMINI_MODEL=gemini-2.0-flash
WEB_APP_URL=https://dev-aida.beings.com

JWT_SECRET=aIdAdEv-secret

# Posthog
POSTHOG_KEY=phc_N5CrNROXfSJpqANrHYrnybXhjWTQR2wuVoboDpn3eKN
POSTHOG_HOST=https://us.i.posthog.com
