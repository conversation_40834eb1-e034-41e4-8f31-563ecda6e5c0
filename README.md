## Server for Aida project

# Pre-requisites

- Install Node 20
- Install Yarn
- Install Postgres
- Install GCP CLI to login

# Getting started

- Install dependencies

```
yarn install
```

- Login into GCP account for auth

```
gcloud auth application-default login
```

- Run command to fetch secrets from GCP SecretManager and generate local `.env` file

```
yarn load-env
```

- Modify the `.env` accordingly to local usage. Such as: `PORT`; `DB_HOST`; `DB_PASSWORD`; etc...
- Start server

```
yarn dev
```

Server should be ready on `http://localhost:4000`
