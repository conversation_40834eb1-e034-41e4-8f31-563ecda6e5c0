.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/sdks
!.yarn/versions

# Swap the comments on the following lines if you wish to use zero-installs
# In that case, don't forget to run `yarn config set enableGlobalCache false`!
# Documentation here: https://yarnpkg.com/features/caching#zero-installs

#!.yarn/cache
.pnp.*

/node_modules
**/*/node_modules
.yarn/*

.DS_Store

dist/
.env
env/.env.local

sendgrid.env
local-dev/

# Cursor
.cursor/*
.cursor/