#!/bin/bash

# Get all versions with 0% traffic split
echo "Cleaning up old versions..."
VERSIONS_TO_DELETE=$(gcloud app versions list --format="table[no-heading](version.id)" --filter="traffic_split=0" | tail -n +2)

# Delete all versions except the latest one
if [ ! -z "$VERSIONS_TO_DELETE" ]; then
    echo "Deleting old versions..."
    for version in $VERSIONS_TO_DELETE; do
        gcloud app versions delete "$version" --quiet
    done
    echo "Cleanup completed"
else
    echo "No versions to delete"
fi 

# Deploy the new version
echo "Deploying new version..."
gcloud app deploy app.yaml --verbosity="debug"