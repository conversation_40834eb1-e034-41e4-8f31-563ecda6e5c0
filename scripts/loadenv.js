const fs = require("fs");
const path = require("path");
const { SecretManagerServiceClient } = require("@google-cloud/secret-manager");
require("dotenv").config();

// List of secret keys to fetch from GCP Secret Manager
const SECRET_KEYS = [
  "GOOGLE_GENERATIVE_AI_API_KEY",
  "DB_PASSWORD",
  "REV_AI_TOKEN",
  "SENDGRID_API_KEY",
  "SPEECHMATICS_API_KEY",
];

let env = "development";
if (process.env.BUILD_MODE) {
  env = process.env.BUILD_MODE;
}

console.log(`Selecting environment: ${env}`);
const filePath = path.join(__dirname, "../env", `/.env.${env}`);
const envFilePath = path.join(__dirname, "../", "/.env");

// Copy the base .env file
fs.copyFileSync(filePath, envFilePath);

// Initialize Secret Manager client
let secretManagerClient;
try {
  secretManagerClient = new SecretManagerServiceClient();
} catch (error) {
  console.error(
    "\nError: Could not initialize Google Cloud Secret Manager client."
  );
  console.error("\nTo fix this, you need to set up Google Cloud credentials:");
  console.error(
    "\nOption 1 - Using gcloud CLI (Recommended for local development):"
  );
  console.error("1. Install gcloud CLI: brew install google-cloud-sdk");
  console.error("2. Login: gcloud auth login");
  console.error(
    "3. Set up application default credentials: gcloud auth application-default login"
  );
  console.error("4. Set project: gcloud config set project aida-22a9a");
  console.error("\nOption 2 - Using service account key:");
  console.error("1. Create a service account key from Google Cloud Console");
  console.error(
    '2. Set environment variable: export GOOGLE_APPLICATION_CREDENTIALS="/path/to/your/service-account-key.json"'
  );
  process.exit(1);
}

async function fetchSecrets() {
  try {
    const projectId = process.env.GCP_PROJECT_ID;
    console.log(`Using GCP Project ID: ${projectId}`);

    // Fetch each secret and append to .env file
    for (const key of SECRET_KEYS) {
      try {
        const [version] = await secretManagerClient.accessSecretVersion({
          name: `projects/${projectId}/secrets/${key}/versions/latest`,
        });

        const secretValue = version.payload.data.toString();
        fs.appendFileSync(envFilePath, `\n${key}=${secretValue}`);
        console.log(`Successfully fetched secret: ${key}`);
      } catch (error) {
        if (error.code === 5) {
          // NOT_FOUND
          console.error(`Secret ${key} not found in project ${projectId}`);
        } else if (error.code === 7) {
          // PERMISSION_DENIED
          console.error(
            `Permission denied to access secret ${key}. Please check your credentials and IAM permissions.`
          );
        } else {
          console.error(`Error fetching secret ${key}:`, error.message);
        }
      }
    }
  } catch (error) {
    console.error("Error initializing Secret Manager:", error.message);
    if (error.message.includes("Could not load the default credentials")) {
      console.error(
        "\nPlease ensure you have set up Google Cloud credentials properly."
      );
      console.error("See the error message above for setup instructions.");
    }
  }
}

// Execute the secret fetching
fetchSecrets().catch(console.error);
