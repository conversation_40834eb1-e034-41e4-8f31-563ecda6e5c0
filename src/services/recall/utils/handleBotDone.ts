import { v4 as uuidv4 } from "uuid";
import { recallAiClient } from "..";
import fs from "fs-extra";
import { updateSessionByBotId } from "@/schemas/session/utilts";
import { log } from "@/services/logger";
import downloadFileFromUrl from "@/utils/downloadFileFromUrl";
import { gcsUpload } from "@/services/storage";
import { gcsResourceFolder } from "@/config";
import { IEUploadAction } from "@/schemas/insightEngine/InsightEngine.model";
import { Session, SessionStatus } from "@/schemas/session/Session.model";
import { createNewResource } from "@/schemas/resource/utils";
import { models } from "@/schemas";

const processRecording = async (
  botId: string,
  videoUrl: string,
  session: Session
) => {
  const fileName = `recall-record-${botId}-${uuidv4()}.mp4`;
  const filePath = `${gcsResourceFolder}/${fileName}`;
  const userId = session.createdById;
  const title = session.title;

  const downloadedFile = await downloadFileFromUrl(videoUrl, fileName);

  // Upload the file to GCS
  await gcsUpload(downloadedFile.path, filePath, downloadedFile.mimetype, true);

  // Remove the file after uploaded to GCS
  fs.rm(downloadedFile.path).catch(log.stack);

  // Start processing the file and create new resource
  await createNewResource({
    gcsFilePath: filePath,
    fileName,
    fileSize: downloadedFile.size,
    userId,
    title,
    uploadAction: IEUploadAction.RECORD,
    sessionId: session.id,
    projectId: session.projectId,
  });
};

const handleBotDone = async (botId: string) => {
  try {
    const bot = await recallAiClient.retrieveBot(botId);
    log.debug("[handleBotDone] Start processing Recall bot done meeting", bot);
    if (!bot || !bot.video_url) {
      return;
    }

    const session = await models.Session.xFind1By("recallBotId", botId);
    if (!session) {
      log.debug("[handleBotDone] No session found");
      return;
    }

    log.debug("[handleBotDone] Found session", session);

    if (
      [SessionStatus.Completed, SessionStatus.Processing].includes(
        session.status
      )
    ) {
      log.debug(
        "[handleBotDone] Session is already completed or processing",
        session
      );
      return;
    }

    log.debug("[handleBotDone] Update session status to processing");
    await updateSessionByBotId(botId, {
      videoUrl: bot.video_url,
      status: SessionStatus.Processing,
    });
    log.debug("[handleBotDone] Start processing recording", bot.video_url);
    await processRecording(botId, bot.video_url, session);
  } catch (e) {
    await updateSessionByBotId(botId, {
      status: SessionStatus.Failed,
    });
    log.error(`[handleBotDone] Error: ${e}`);
  }
};

export default handleBotDone;
