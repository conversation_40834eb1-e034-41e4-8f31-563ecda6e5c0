import { initializeApp, cert, getApps } from "firebase-admin/app";
import { getAuth } from "firebase-admin/auth";
import { getFirestore } from "firebase-admin/firestore";
import { defaultTimezone, getGcsServiceAccount } from "@/config";
import admin from "firebase-admin";
import { FirebaseUser } from "@/types/user";

// Re-export FirebaseUser type
export { FirebaseUser };

export let app: admin.app.App | undefined;
let auth: admin.auth.Auth;
let db: admin.firestore.Firestore;

export const initializeFirebase = async () => {
  // Check if Firebase is already initialized
  const existingApps = getApps();
  if (existingApps.length > 0) {
    app = existingApps[0] as admin.app.App;
    auth = getAuth(app);
    db = getFirestore(app);
    return { app, auth, db };
  }

  if (!app) {
    const serviceAccount = await getGcsServiceAccount();
    app = initializeApp({
      credential: cert(serviceAccount),
    }) as admin.app.App;
    auth = getAuth(app);
    db = getFirestore(app);
  }
  return { app, auth, db };
};

export const getFirebaseAuth = async () => {
  const { auth } = await initializeFirebase();
  return auth;
};

export const getFirebaseDb = async () => {
  const { db } = await initializeFirebase();
  return db;
};

export const getFirebaseUserByEmail = async (email: string) => {
  const auth = await getFirebaseAuth();
  const user = await auth.getUserByEmail(email);
  return user;
};

export const getFirebaseUserById = async (uid: string) => {
  const auth = await getFirebaseAuth();
  const user = await auth.getUser(uid);
  return user;
};

export const getFirebaseListOfUsersByIds = async (uids: string[]) => {
  const auth = await getFirebaseAuth();
  const getUsersResult = await auth.getUsers(uids.map((uid) => ({ uid })));
  return getUsersResult.users;
};

export const getFirebaseListOfUsersByEmails = async (emails: string[]) => {
  const auth = await getFirebaseAuth();
  const getUsersResult = await auth.getUsers(
    emails.map((email) => ({ email }))
  );
  return getUsersResult.users;
};

export const getFirebaseUserDetails = async (
  uid: string
): Promise<FirebaseUser> => {
  const db = await getFirebaseDb();
  const doc = await db.collection("users").doc(uid).get();
  if (!doc.exists) {
    throw new Error(`User ${uid} not found`);
  }
  return doc.data() as FirebaseUser;
};

export const syncUsersHandler = async () => {
  const auth = await getFirebaseAuth();
  const db = await getFirebaseDb();
  const result = await auth.listUsers(1000);

  for (const user of result.users) {
    const providerIds = user.providerData.map((p) => p.providerId);

    // Filter users with Google or Microsoft login
    const isGoogleOrMicrosoft =
      providerIds.includes("google.com") ||
      providerIds.includes("microsoft.com");

    if (isGoogleOrMicrosoft) {
      const userDoc = db.collection("users").doc(user.uid);

      const userData = {
        uid: user.uid,
        email: user.email || null,
        displayName: user.displayName || null,
        preferences: {
          timezone: defaultTimezone,
        },
      };

      try {
        await userDoc.set(userData, { merge: true });
        console.log(`✔️ Synced: ${user.email}`);
      } catch (error) {
        console.error(`❌ Failed to sync ${user.email}:`, error.message);
      }
    }
  }
};

// Export a function to get the initialized app
export const getFirebaseApp = async () => {
  const { app } = await initializeFirebase();
  return app;
};
