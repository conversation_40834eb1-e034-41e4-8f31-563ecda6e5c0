import posthog from "./posthog";
import { EVENTS } from "./posthog";

export const FEATURE_FLAGS = {
  INTERNAL: "internal",
  // Add more feature flags here
} as const;

type FeatureFlagName = (typeof FEATURE_FLAGS)[keyof typeof FEATURE_FLAGS];

class FeatureFlagError extends Error {
  constructor(message: string) {
    super(message);
    this.name = "FeatureFlagError";
  }
}

/**
 * Check if a feature flag is enabled for a user
 * @param userId The user's ID
 * @param flagName The name of the feature flag to check
 * @param defaultValue Default value if the flag check fails
 * @returns Promise<boolean> Whether the feature flag is enabled
 */
export const isFeatureEnabled = async (
  userId: string,
  flagName: FeatureFlagName,
  defaultValue: boolean = false
): Promise<boolean> => {
  try {
    if (!userId) {
      throw new FeatureFlagError("User ID is required");
    }

    // Always check feature flag in PostHog for latest value
    const isEnabled = await posthog.isFeatureEnabled(flagName, userId);

    // Track feature flag check
    posthog.capture({
      distinctId: userId,
      event: EVENTS.FEATURE_FLAG_CHECKED,
      properties: {
        flagName,
        isEnabled,
      },
    });

    return isEnabled;
  } catch (error) {
    console.error(`Error checking feature flag ${flagName}:`, error);
    return defaultValue;
  }
};

/**
 * Check multiple feature flags at once
 * @param userId The user's ID
 * @param flagNames Array of feature flag names to check
 * @returns Promise<Record<FeatureFlagName, boolean>> Object containing the state of each flag
 */
export const areFeaturesEnabled = async (
  userId: string,
  flagNames: FeatureFlagName[]
): Promise<Record<FeatureFlagName, boolean>> => {
  try {
    if (!userId) {
      throw new FeatureFlagError("User ID is required");
    }

    const results = await Promise.all(
      flagNames.map((flagName) => isFeatureEnabled(userId, flagName))
    );

    return Object.fromEntries(
      flagNames.map((flagName, index) => [flagName, results[index]])
    ) as Record<FeatureFlagName, boolean>;
  } catch (error) {
    console.error("Error checking multiple feature flags:", error);
    return Object.fromEntries(
      flagNames.map((flagName) => [flagName, false])
    ) as Record<FeatureFlagName, boolean>;
  }
};

/**
 * Get all feature flags for a user
 * @param userId The user's ID
 * @returns Promise<Record<string, boolean>> Object containing all feature flags and their states
 */
export const getAllFeatureFlags = async (
  userId: string
): Promise<Record<string, boolean>> => {
  try {
    if (!userId) {
      throw new FeatureFlagError("User ID is required");
    }

    const flags = await posthog.getAllFlags(userId);
    // Convert all values to boolean
    return Object.fromEntries(
      Object.entries(flags).map(([key, value]) => [key, Boolean(value)])
    );
  } catch (error) {
    console.error("Error getting all feature flags:", error);
    return {};
  }
};
