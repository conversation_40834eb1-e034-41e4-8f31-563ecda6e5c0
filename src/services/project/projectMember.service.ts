import ProjectMemberModel, { ProjectMemberRole } from "@/schemas/project/ProjectMember.model";
import { createLogger } from "../logger";
import { getProjectById } from "./project.service";
import { UpdateProjectMemberDTO, UpdateProjectMemberParamsDTO } from "@/validators/project.validators";
import { fireBaseUserListToIndexMap } from "@/utils";
import { getFirebaseListOfUsersByIds } from "../firebase";
import ProjectModel from "@/schemas/project/Project.model";
import { createSuccessResponse, createErrorResponse } from "@/utils/response";

const logger = createLogger('ProjectMemberService');

interface AddProjectMemberPayload {
    projectId: string;
    userId: string;
    role: ProjectMemberRole;
}

/**
 * Add a project member to a project
 * @param projectId - The id of the project
 * @param userId - The id of the user
 * @param role - The role of the user
 * @returns The added project member
 */
export const addProjectMember = async ({ projectId, userId, role }: AddProjectMemberPayload) => {
    logger.info(`Adding project member ${userId} to project ${projectId} with role ${role}`);

    const getProjectResult = await getProjectById(projectId);

    if (!getProjectResult.success) {
        return getProjectResult;
    }

    const existingProjectMember = await ProjectMemberModel.xFind1({
        projectId,
        userId
    });

    if (existingProjectMember) {
        logger.warn(`Project member ${userId} already exists in project ${projectId}`);
        return createSuccessResponse(
            existingProjectMember,
            "Project member already exists",
            200
        );
    }

    const newProjectMember = await ProjectMemberModel.xCreate({
        projectId,
        userId,
        role
    });

    return createSuccessResponse(
        newProjectMember,
        "Project member added",
        200
    );
};

/**
 * Get all project members for a project
 * @param projectId - The id of the project
 * @returns The project members
 */
export const getProjectMembers = async (projectId: string) => {
    logger.info(`Getting project members for project ${projectId}`);
    const projectMembers = await ProjectMemberModel.xFind({
        projectId
    });

    logger.info(`Found ${projectMembers.length} project members for project ${projectId}`);

    const userIds = projectMembers.map((projectMember) => projectMember.userId);
    const users = await getFirebaseListOfUsersByIds(userIds);
    const usersMap = fireBaseUserListToIndexMap(users);
    const projectMembersWithUser = projectMembers.map((projectMember) => {
        const userIndex = usersMap[projectMember.userId];
        return {
            ...projectMember,
            user: users?.[userIndex]?.providerData?.[0] || null,
        };
    });

    return createSuccessResponse(
        projectMembersWithUser,
        "Project members fetched",
        200
    );
}


export const getProjectMember = async (projectId: string, userId: string, role?: ProjectMemberRole) => {
    logger.info(`Getting project member ${userId} for project ${projectId}`);

    const where: any = {
        projectId,
        userId
    };

    if (role) {
        where.role = role;
    }

    const projectMember = await ProjectMemberModel.xFind1(where);

    if (!projectMember) {
        return createErrorResponse(
            "Project member not found",
            404
        );
    }

    return createSuccessResponse(
        projectMember,
        "Project member fetched",
        200
    );
};

/**
 * Remove a project member from a project
 * @param projectId - The id of the project
 * @param userId - The id of the user
 * @returns The removed project member
 */
export const removeProjectMember = async (projectId: string, userId: string) => {
    logger.info(`Removing project member ${userId} from project ${projectId}`);

    const projectMember = await getProjectMember(projectId, userId);
    if (!projectMember.success) {
        return projectMember;
    }

    await ProjectMemberModel.xDestroy({
        projectId,
        userId
    });

    return createSuccessResponse(
        projectMember.data,
        "Project member removed",
        200
    );
};


export const changeRoleOfProjectMember = async ({ projectId, memberUserId, role }: UpdateProjectMemberParamsDTO & UpdateProjectMemberDTO) => {
    logger.info(`Changing role of project member ${memberUserId} for project ${projectId} to role ${role}`);

    const projectMember = await getProjectMember(projectId, memberUserId);

    if (!projectMember.success) {
        return projectMember;
    }

    const updatedProjectMember = await ProjectMemberModel.xUpdate({
        projectId,
        userId: memberUserId
    }, {
        role
    });

    logger.info(`Project member ${memberUserId} updated for project ${projectId} to role ${role}`);

    return createSuccessResponse(
        updatedProjectMember,
        "Project member role updated",
        200
    );
};


export const getProjectMemberWithRelations = async (projectId: string, userId: string) => {
    const projectMember = await ProjectMemberModel.findOne({
        where: {
            projectId,
            userId
        },
        include: {
            model: ProjectModel,
            as: "project",
        }
    });

    if (!projectMember) {
        return createErrorResponse(
            "Project member not found",
            404
        );
    }

    return createSuccessResponse(
        projectMember,
        "Project member fetched",
        200
    );
}

export const getAllProjectMembersByUserId = async (userId: string) => {
    const projectMembers = await ProjectMemberModel.xFind({
        userId
    });

    return createSuccessResponse(
        projectMembers,
        "Project members fetched",
        200
    );
}


export const verifyProjectEditor = async (userId: string, projectId: string) => {
    const getProjectMemberResult = await getProjectMember(projectId, userId, ProjectMemberRole.EDITOR);

    if (!getProjectMemberResult.success) {
        return createErrorResponse(
            "User is not a project editor",
            getProjectMemberResult.statusCode
        );
    }

    return createSuccessResponse(
        getProjectMemberResult.data,
        "User is a project editor",
        200
    );
}

export const deleteProjectMember = async (projectId: string, userId: string) => {
    const getProjectMemberResult = await getProjectMember(projectId, userId);
    if (!getProjectMemberResult.success) {
        return getProjectMemberResult;
    }

    await ProjectMemberModel.xDestroy({
        projectId,
        userId
    });

    return createSuccessResponse(
        true,
        "Project member deleted",
        200
    );
}

/**
 * Allow a user to leave a project
 * @param projectId - The id of the project
 * @param userId - The id of the user leaving the project
 * @returns The result of the leave operation
 */
export const leaveProject = async (projectId: string, userId: string) => {
    // First check if the project exists
    const getProjectResult = await getProjectById(projectId);
    if (!getProjectResult.success) {
        return getProjectResult;
    }

    const project = getProjectResult.data;

    // Check if user is actually a member of the project
    const projectMember = await getProjectMember(projectId, userId);
    if (!projectMember.success) {
        return createErrorResponse(
            "You are not a member of this project",
            404
        );
    }

    // Check if user is the project owner
    const isProjectOwner = project.createdById === userId;
    
    if (isProjectOwner) {
        // Check if there are other members in the project
        const allMembers = await ProjectMemberModel.xFind({ projectId });
        
        if (allMembers.length === 1) {
            // Owner is the only member, prevent leaving
            return createErrorResponse(
                "You cannot leave the project as you are the only member. Please delete the project instead or add another member first.",
                403
            );
        }
        
        logger.warn(`Project owner ${userId} is leaving project ${projectId} with other members present`);
    }

    // Remove the user from the project
    await ProjectMemberModel.xDestroy({
        projectId,
        userId
    });

    return createSuccessResponse(
        {
            projectId,
            userId,
            leftAt: new Date()
        },
        "You have successfully left the project",
        200
    );
};
