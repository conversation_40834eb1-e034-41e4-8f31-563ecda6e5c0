import ProjectInvitationModel, { InvitationStatus, ProjectInvitation } from "@/schemas/project/ProjectInvitation.model";
import { ProjectMemberRole } from "@/schemas/project/ProjectMember.model";
import { Op } from "sequelize";
import { ulid } from "ulidx";
import { addProjectMember } from "./projectMember.service";
import { createLogger } from "@/services/logger";
import { getProjectById } from "./project.service";
import { sendProjectInvitationEmail } from "../email";
import { getFirebaseListOfUsersByEmails, getFirebaseUserByEmail } from "../firebase";
const logger = createLogger('ProjectInvitationService');

interface CreateProjectInvitationPayload {
    projectId: string;
    email: string;
    role: ProjectMemberRole;
    expireDays?: number;
    senderEmail?: string;
    forceSendEmail?: boolean;
}

/**
 * Create a project invitation
 * @param projectId - The id of the project
 * @param email - The email of the user
 * @param role - The role of the user
 * @param expireDays - The number of days the invitation will be valid, default is 2 days
 */
export const createProjectInvitation = async ({
    projectId,
    email,
    role = ProjectMemberRole.VIEWER,
    expireDays = 2,
    senderEmail = '',
    forceSendEmail = false,
}: CreateProjectInvitationPayload) => {

    const getProjectResult = await getProjectById(projectId);

    if (!getProjectResult.success) {
        return getProjectResult;
    }

    const project = getProjectResult.data;

    const [getSenderUserResult, getRecipientUserResult] = await Promise.all([
        getFirebaseUserByEmail(senderEmail),
        getFirebaseUserByEmail(email)
    ]);

    const projectOwnerName = getSenderUserResult?.displayName;
    const recipientName = getRecipientUserResult?.displayName;

    const projectNotExistValidation = await validateBeforeCreateNewInvitation(projectId, email);

    if (!projectNotExistValidation.isValid) {

        if (forceSendEmail) {
            await sendProjectInvitationEmail({
                to: email,
                projectName: project.name,
                code: projectNotExistValidation.data.code,
                projectId,
                message: `You have been invited to collaborate on project: ${project.name} on AIDA`,
                invitedByUserEmail: senderEmail,
                projectOwnerName,
                recipientName: recipientName || 'there',
            });
        }

        return {
            success: true,
            message: projectNotExistValidation.errorMessage,
            statusCode: 200
        }
    }

    logger.info(`Creating invitation for ${email} to project ${projectId}`);
    const code = ulid();
    const projectInvitation = await ProjectInvitationModel.xCreate({
        projectId,
        email,
        role,
        code,
        expiredAt: new Date(Date.now() + expireDays * 24 * 60 * 60 * 1000),
    });

    logger.debug(`Created project invitation with code=[${code}] for ${email} with role=[${role}] of project=[${projectId}]`);

    // should send email to the user
    await sendProjectInvitationEmail({
        to: email,
        projectName: project.name,
        code,
        projectId,
        invitedByUserEmail: senderEmail,
    });

    return {
        success: true,
        message: "Project invitation created",
        statusCode: 200,
        data: projectInvitation
    }
};

export const getProjectInvitation = async (projectId: string, email: string) => {
    logger.info(`Getting invitation for ${email} in project ${projectId}`);
    const projectInvitation = await ProjectInvitationModel.xFind1({
        projectId,
        email
    });

    return projectInvitation;
};

export const deleteProjectInvitation = async (projectId: string, email: string) => {
    logger.info(`Deleting invitation for ${email} from project ${projectId}`);
    const projectInvitation = await ProjectInvitationModel.xDestroy({
        projectId,
        email
    });

    return {
        success: true,
        message: "Project invitation deleted",
        statusCode: 200,
        data: projectInvitation
    }
};

export const getProjectInvitations = async (projectId: string, status?: InvitationStatus) => {
    logger.info(`Getting all invitations for project ${projectId}`);
    const projectInvitations = await ProjectInvitationModel.xFind({
        projectId,
        ...(status ? { status } : {})
    }, {
        order: [["createdAt", "DESC"]]
    });

    const firebaseUsers = await getFirebaseListOfUsersByEmails(projectInvitations.map((invitation) => invitation.email));

    logger.debug(`Found ${projectInvitations.length} invitations`);

    return {
        success: true,
        message: "Project invitations fetched",
        statusCode: 200,
        data: projectInvitations.map((invitation) => ({
            ...invitation,
            user: firebaseUsers.find((user) => user.email === invitation.email)
        }))
    }
};



interface VerifyProjectInvitationPayload {
    code: string;
    userId: string;
    email: string;
}
/**
 * Verify a project invitation and add the user to the project as a member with VIEWER role
 * @param code - The code of the project invitation
 * @param userId - The id of the user
 * @returns The project invitation
 */
export const verifyProjectInvitation = async ({ code, userId, email }: VerifyProjectInvitationPayload) => {
    logger.info(`Verifying invitation with code ${code} for user ${userId} with email ${email}`);

    const projectInvitation = await ProjectInvitationModel.xFind1({
        code,
        expiredAt: {
            [Op.gt]: new Date()
        }
    });

    const { errorMessage, isValid } = validateInvitationBeforeVerify(projectInvitation, email);

    if (!isValid) {
        return {
            success: false,
            message: errorMessage,
            statusCode: 400
        }
    }

    await ProjectInvitationModel.xUpdate({
        code
    }, {
        status: InvitationStatus.APPROVED,
        userId
    });

    logger.info(`Invitation verified successfully: ${code}`);

    const addProjectMemberResult = await addProjectMember({
        projectId: projectInvitation.projectId,
        userId,
        role: projectInvitation.role as ProjectMemberRole,
    });

    if (!addProjectMemberResult.success) {
        return addProjectMemberResult;
    }

    return {
        success: true,
        message: "Project invitation verified",
        statusCode: 200,
        data: {
            invitationId: projectInvitation.id,
            projectId: projectInvitation.projectId,
            role: projectInvitation.role,
        }
    }
};

/***
 * ------------------------------------------------------------------------------------------------
 *                              PRIVATE FUNCTIONS
 * ------------------------------------------------------------------------------------------------
 * 
 * Below functions are private and should not be used outside of this file
 * 
 * Separated from the main functions to make the code more readable and easier to maintain
 */

/**
 * Validate the project invitation before verifying it
 * @param projectInvitation - The project invitation
 * @param email - The email of the user
 * @returns The validation result
 */
function validateInvitationBeforeVerify(projectInvitation: ProjectInvitation | null | undefined, email: string) {
    if (!projectInvitation) {
        logger.warn(`[validateInvitationBeforeVerify] Invitation not exists`);
        return {
            errorMessage: `Project invitation not found`,
            isValid: false
        }
    }

    if (projectInvitation.expiredAt && projectInvitation.expiredAt < new Date()) {
        logger.warn(`[${projectInvitation.id}] Invitation expired: ${projectInvitation.expiredAt}`);
        return {
            errorMessage: `Project invitation link is expired`,
            isValid: false
        }
    }

    if (projectInvitation.status !== InvitationStatus.SENT) {
        logger.warn(`[${projectInvitation.id}] Invalid invitation link is already used. Current status: ${projectInvitation.status}`);
        return {
            errorMessage: `Project invitation link is already used`,
            isValid: false
        }
    }

    if (projectInvitation.email && projectInvitation.email !== email) {
        logger.warn(`[${projectInvitation.id}] Invitation email mismatch: ${projectInvitation.email} !== ${email}`);
        return {
            errorMessage: `Project invitation email mismatch`,
            isValid: false
        }
    }

    return {
        errorMessage: '',
        isValid: true
    }
}


/**
 * Validate before creating a new invitation, check if the project already exists
 * @param projectId - The id of the project
 * @param email - The email of the user
 * @returns The validation result
 */
async function validateBeforeCreateNewInvitation(projectId: string, email: string) {
    // Check if the project already exists
    logger.info(`[validateBeforeCreateNewInvitation] Checking if project ${projectId} already exists...`);
    const invitation = await ProjectInvitationModel.xFind1({
        projectId,
        email,
        expiredAt: {
            [Op.gt]: new Date()
        }
    });

    if (invitation) {
        const errorMessage = `Project ${projectId} access already invited to ${email}`;
        logger.warn(errorMessage);
        return {
            errorMessage,
            isValid: false,
            data: invitation
        }
    }

    return {
        errorMessage: '',
        isValid: true,
        data: invitation
    }
}

export const deleteProjectInvitationOfUser = async (projectId: string, userId: string) => {
    await ProjectInvitationModel.xDestroy({
        projectId,
        userId
    });
}