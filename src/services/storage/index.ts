import type { Bucket, UploadOptions } from "@google-cloud/storage";
import { Storage } from "@google-cloud/storage";
import { gcsBucket, getGcsServiceAccount } from "@/config";
import { log } from "@/services/logger";

let _bucket: Bucket | undefined;
let _storage: Storage | undefined;

const getStorage = async () => {
  if (!_storage) {
    const serviceAccount = await getGcsServiceAccount();
    _storage = new Storage({ credentials: serviceAccount });
  }
  return _storage;
};

const getBucket = async () => {
  if (!_bucket) {
    const storage = await getStorage();
    _bucket = storage.bucket(gcsBucket);
  }
  return _bucket;
};

export const gcsUpload = async (
  filePath: string,
  fileName: string,
  mineType?: string,
  shouldCache?: boolean
) => {
  const uploadOptions: UploadOptions = {
    destination: fileName,
    contentType: mineType,
  };

  if (shouldCache) {
    Object.assign(uploadOptions, {
      metadata: {
        cacheControl: "public, max-age=********",
        contentDisposition:
          "attachment" + (fileName ? `; filename="${fileName}"` : ""),
      },
    });
  }

  const bucket = await getBucket();
  await bucket.upload(filePath, uploadOptions);
  return fileName;
};

export const deleteFile = async (fileName: string) => {
  const bucket = await getBucket();
  if (!fileName) return;

  try {
    await bucket.file(fileName).delete();
  } catch (error) {
    log.error(`Failed to delete file: ${error.message}`);
  }
};

/**
 * Get the size of a file from Google Cloud Storage
 * @param fileName - The name of the file in GCS
 * @returns The size of the file in bytes, or undefined if the file doesn't exist or there's an error
 */
export const getFileSize = async (fileName: string): Promise<number | undefined> => {
  if (!fileName) return undefined;
  
  const bucket = await getBucket();

  try {
    const [metadata] = await bucket.file(fileName).getMetadata();
    return Number(metadata.size);
  } catch (error) {
    log.error(`Failed to get file size for: ${fileName}`, error);
    return undefined;
  }
};

export const generateSignedUrlForRead = async (fileName: string) => {
  if (!fileName) return;

  const bucket = await getBucket();

  try {
    const [signedUrl] = await bucket.file(fileName).getSignedUrl({
      version: "v4",
      action: "read",
      expires: Date.now() + 7 * 24 * 60 * 60 * 1000, // 7 days
    });

    return signedUrl;
  } catch (error) {
    log.error(`Failed to generate signed URL for: ${fileName}`, error);
    return undefined;
  }
};

export const generateSignedUrlForUpload = async (
  fileName: string,
  folder?: string
) => {
  const bucket = await getBucket();

  const uploadFileName = folder ? `${folder}/${fileName}` : fileName;

  const [response] = await bucket
    .file(uploadFileName)
    .generateSignedPostPolicyV4({
      expires: Date.now() + 120 * 60 * 1000, // 2 hours
      fields: {
        "x-ignore-file-name": fileName,
      },
    });

  return response;
};
