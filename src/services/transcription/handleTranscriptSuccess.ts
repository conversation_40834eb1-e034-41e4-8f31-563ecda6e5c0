import { models } from "@/schemas";
import type { Monologue, RevAi<PERSON><PERSON>Job } from "revai-node-sdk";
import { revAiClient } from "../revAi";
import { TranscriptStatus } from "@/schemas/resource/ResourceInInsightEngine.model";
import { upsertUserStatistics } from "@/schemas/statistics/utils";
import { submitExtractionJob } from ".";
import { log } from "../logger";
import { SessionStatus } from "@/schemas/session/Session.model";
import { generateAndSendSummary } from "./generateSummary";
import { getFirebaseAuth } from "../firebase";
import { addDays } from "date-fns";
import { DATA_RETENTION_DAYS } from "@/config";

export const handleTranscriptSuccess = async (job: RevAiApiJob) => {
  try {
    const resourceInIE = await models.ResourceInInsightEngine.xFind1By(
      "revAiJobId",
      job.id
    );
    if (!resourceInIE) {
      return;
    }
    const resource = await models.Resource.xFind1By(
      "id",
      resourceInIE.resourceId
    );
    const data = await revAiClient.getTranscriptObject(job.id);

    if (!data) {
      return;
    }

    // Submit topic extraction job
    // TODO: Enable this later after migrate RevAI to US region
    // log.info("[handleTranscriptSuccess] Submitting topic extraction job");
    // const extractionJob = await submitExtractionJob(data);

    // await models.ResourceInInsightEngine.xUpdateById(resourceInIE.id, {
    //   revAiExtractionJobId: extractionJob.id,
    //   status: TranscriptStatus.InProgress,
    // });

    // Create transcriptions
    log.info("[handleTranscriptSuccess] Creating transcriptions");
    const transcriptions = await Promise.all(
      data.monologues.map((i) => createTranscript(i, resourceInIE.id))
    );

    const wordsCount = transcriptions.reduce((acc, curr) => {
      const text = (curr?.content ?? "").trim();
      acc += text.split(" ").length;
      return acc;
    }, 0);

    // Update user statistics
    log.info("[handleTranscriptSuccess] Updating user statistics");
    await upsertUserStatistics(resource.createdById, {
      totalTranscriptionsWordCount: wordsCount,
    });

    // Update transcription job status
    log.info("[handleTranscriptSuccess] Updating transcription job status");
    await models.ResourceInInsightEngine.xUpdateById(resourceInIE.id, {
      status: TranscriptStatus.Completed,
    });

    // Update session status if it exists
    if (resource.sessionId) {
      log.debug("[handleTranscriptSuccess] Update session status to completed");
      const sessionId = await models.Session.xUpdateById(resource.sessionId, {
        status: SessionStatus.Completed,
      });

      const session = await models.Session.xFind1ById(resource.sessionId);
      if (session.shouldSendSummaryToEmail) {
        // create retention setting for the resource
        await models.RetentionSetting.xCreate({
          resourceId: resource.id,
          expiryDate: addDays(new Date(), DATA_RETENTION_DAYS),
        });
        await handleSendSummaryEmail(session.meetingUrl, resourceInIE.id);
      }
    }
  } catch (error) {
    log.error(
      "[handleTranscriptSuccess] Error in handleTranscriptSuccess:",
      error
    );
  }
};

const createTranscript = async (
  m: Monologue,
  resourceInInsightEngineId: string
) => {
  const startTime = Math.min(
    ...m.elements.filter((i) => i.ts).map((e) => e.ts as number)
  );
  const endTime = Math.max(...m.elements.map((e) => e.end_ts ?? 0));
  const name = `Speaker ${m.speaker}`;
  let content = "";
  m.elements.forEach((e) => (content += e.value));

  const transcription = await models.Transcription.xCreate({
    resourceInInsightEngineId,
    content,
    startTime,
    nameFromRevAi: name,
    endTime,
  });
  return transcription;
};

export const handleSendSummaryEmail = async (
  meetingUrl: string,
  riieId: string
) => {
  const sessions = await models.Session.xFindBy("meetingUrl", meetingUrl);
  if (sessions.length === 0) {
    return;
  }
  const receiverUids = sessions
    .filter((s) => s.shouldSendSummaryToEmail)
    .map((s) => ({ uid: s.createdById }));
  const firebaseAuth = await getFirebaseAuth();
  const fUsers = (await firebaseAuth.getUsers(receiverUids)).users;
  if (fUsers.length === 0) {
    return;
  }
  const receivers = fUsers.map((fUser) => ({
    email: fUser.email,
    name: fUser.displayName,
  }));

  await generateAndSendSummary({
    resourceInInsightEngineId: riieId,
    receivers,
    sessionTitle: sessions[0].title,
    sessionDate: sessions[0].startTime.toISOString(),
  });
};
