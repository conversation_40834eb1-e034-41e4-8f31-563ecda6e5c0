import { Document, Packer, Paragraph, TextRun } from 'docx'

import { models } from "@/schemas";
import { sendSummary } from '../email';
import { SummarySection } from '../email/template/summary';
import { summaryConservationWithPrompt } from '../geminiAi';
import { aidaEndpoint } from '@/config';

export type UserType = {
  email: string
  name: string
}

const SUMMARY_PROMPT = `Write a summary, in UK english, of the transcript from the meeting including:
1. Percentage talk time in the format: Person name: %
2. Meeting Purpose
3. Key Themes
4. Key Topics
5. Key Actions
6. Key Sentiment
7. Quantitative insights
8. Qualitative Insights
9. Questions with responses in the format: Q: Question - A: Answer`

type SectionKey =
  | keyof ReturnType<typeof summaryConservationWithPrompt>
  | 'prompt'

const SUMMARY_SECTIONS: Array<
  Omit<SummarySection, 'content'> & { key: Section<PERSON><PERSON> }
> = [
    { type: 'text', title: 'Purpose', key: 'purpose' },
    {
      type: 'text',
      title: 'Prompt Used for Smart Notes Generation',
      key: 'prompt',
    },
    { type: 'list', title: 'Key Topics', key: 'keyTopics' },
    { type: 'list', title: 'Percentage Talktime', key: 'percentageTalktime' },
    { type: 'list', title: 'Key Themes', key: 'keyThemes' },
    { type: 'text', title: 'Key Actions', key: 'keyActions' },
    { type: 'list', title: 'Key Sentiments', key: 'keySentiments' },
    { type: 'list', title: 'Quantitative Insights', key: 'quantitativeInsights' },
    { type: 'list', title: 'Qualitative Insights', key: 'qualitativeInsights' },
    {
      type: 'list',
      title: 'Questions with Answers',
      key: 'questionsWithAnswers',
    },
  ]

const invalidContent = ['null', 'undefined']

const capitalize = (s: string) =>
  String(s[0]).toUpperCase() + String(s).slice(1)

const defaultFormater = (content: string | string[]): string[] => {
  if (!Array.isArray(content)) {
    if (!content || invalidContent.includes(content)) return ['No content']
    return [capitalize(content)]
  }
  return content.length
    ? invalidContent.includes(content[0])
      ? ['No content']
      : content.map(capitalize)
    : ['No content']
}
const contentFormater: {
  [key in SectionKey]: (content: string | string[]) => string[]
} = {
  prompt: (content: string | string[]) => {
    return content ? [`<i>"${content}"</i>`] : ['No content']
  },
  questionsWithAnswers: (content: string | string[]) => {
    const formatedContent = defaultFormater(content)
    if (['No content', 'null', 'undefined'].includes(formatedContent[0]))
      return ['No question was asked']
    return formatedContent.map(qa => {
      const [question, answer] = qa.split(' - A: ')
      return [question.replace('Q: ', ''), `<ul><li>${answer}</li></ul>`].join(
        '<br>',
      )
    })
  },
  title: defaultFormater,
  purpose: defaultFormater,
  keyTopics: defaultFormater,
  percentageTalktime: defaultFormater,
  keyThemes: defaultFormater,
  keyActions: defaultFormater,
  keySentiments: defaultFormater,
  quantitativeInsights: defaultFormater,
  qualitativeInsights: defaultFormater,
}


const conservationsToDocx = (
  conservations: Parameters<typeof summaryConservationWithPrompt>[0],
) =>
  new Document({
    sections: [
      {
        children: conservations.map(({ speakerName, content }) => {
          const parentRow = new Paragraph({})
          const n = new TextRun({ text: `${speakerName} `, bold: true })
          const c = new TextRun({ text: ` ${content}` })
          const spacing = new TextRun({ text: '', break: 1 })
          parentRow.addChildElement(n)
          parentRow.addChildElement(c)
          parentRow.addChildElement(spacing)
          return parentRow
        }),
      },
    ],
  })


export type GenerateSummaryArgs = {
  resourceInInsightEngineId: string
  receivers: UserType[]
  prompt?: string
  subject?: string
  sessionTitle?: string
  sessionDate?: string
}

const extractFileNameWithoutExtension = (fileName?: string) => {
  if (!fileName) return 'Unknown name'
  const lastDotIndex = fileName.lastIndexOf('.')
  if (lastDotIndex === -1) {
    return fileName
  }
  return fileName.substring(0, lastDotIndex)
}

export type GenerateSummaryResult = {
  summary: ReturnType<typeof summaryConservationWithPrompt>;
  base64Docx: string;
  summaryTitle: string;
  subjectDate: string;
}

export const generateSummary = async ({
  resourceInInsightEngineId,
  sessionTitle,
  prompt,
  sessionDate,
}: Omit<GenerateSummaryArgs, 'receivers' | 'subject'>): Promise<GenerateSummaryResult> => {
  try {
    const transcripts = await models.Transcription.xFind({
      resourceInInsightEngineId,
    })

    if (!transcripts.length) {
      throw new Error('No transcripts found')
    }

    const conversations: Parameters<typeof summaryConservationWithPrompt>[0] =
      transcripts.map(({ nameFromRevAi, content }) => ({
        speakerName: nameFromRevAi,
        content,
      }))

    const doc = conservationsToDocx(conversations)
    const base64Docx = await Packer.toBase64String(doc)

    const targetPrompt = !prompt ? SUMMARY_PROMPT : prompt
    const summary = await summaryConservationWithPrompt(
      conversations,
      targetPrompt,
    )

    let subjectDate = (
      sessionDate ? new Date(sessionDate) : new Date()
    ).toLocaleString('en-GB', {
      day: 'numeric',
      month: 'short',
      year: 'numeric',
      hour: 'numeric',
      minute: 'numeric',
      hour12: true,
    })

    let summaryTitle = sessionTitle || 'Title Unrecognizable'

    const riie = await models.ResourceInInsightEngine.xFind1ById(
      resourceInInsightEngineId,
    )
    if (riie && !sessionTitle && !sessionDate) {
      const resource = await models.Resource.xFind1ById(riie?.resourceId)
      const extractedName = extractFileNameWithoutExtension(resource?.name)
      if (extractedName) summaryTitle = extractedName
      if (resource) {
        subjectDate = new Date(resource.createdAt).toLocaleString('en-GB', {
          day: 'numeric',
          month: 'short',
          year: 'numeric',
          hour: 'numeric',
          minute: 'numeric',
          hour12: true,
        })
      }
    }

    // Store summary in database
    await models.Summary.xCreate({
      resourceId: riie?.resourceId,
      title: summaryTitle,
      purpose: summary.purpose,
      keyTopics: summary.keyTopics,
      percentageTalktime: summary.percentageTalktime,
      keyThemes: summary.keyThemes,
      keyActions: summary.keyActions,
      keySentiments: summary.keySentiments,
      quantitativeInsights: summary.quantitativeInsights,
      qualitativeInsights: summary.qualitativeInsights,
      questionsWithAnswers: summary.questionsWithAnswers,
    })

    return {
      summary,
      base64Docx,
      summaryTitle,
      subjectDate,
    }
  } catch (error) {
    throw error
  }
}

export const sendSummaryEmail = async ({
  summary,
  base64Docx,
  summaryTitle,
  subjectDate,
  receivers,
  subject,
  resourceInInsightEngineId,
}: GenerateSummaryResult & {
  receivers: UserType[];
  subject?: string;
  resourceInInsightEngineId: string;
}) => {
  const summarySections: SummarySection[] = SUMMARY_SECTIONS.map(
    ({ type, title, key }) => {
      const content = contentFormater[key](
        key === 'prompt' ? SUMMARY_PROMPT : summary[key]?.toString() || '',
      )
      return {
        type: [
          'No content',
          'No question was asked',
          'null',
          'undefined',
        ].includes(content[0])
          ? 'text'
          : type,
        title,
        content,
      }
    },
  )
  const riie = await models.ResourceInInsightEngine.xFind1ById(
    resourceInInsightEngineId,
  )
  const transcriptLink = `${aidaEndpoint}/files?resourceId=${riie?.resourceId}`
  const manageSharingLink = `${aidaEndpoint}/manage-transcripts/${riie?.resourceId}`

  const promises = receivers
    .filter(i => i.email && i.name)
    .map(({ email, name }) => {
      if (!email || !name) return
      return sendSummary(
        {
          to: email,
          substitutions: {
            username: name || email,
            transcriptLink,
            manageSharingLink,
          },
          attachments: [],
          subject:
            subject ?? `Smart notes: "${summaryTitle}" @ ${subjectDate}`,
        },
        summarySections,
      )
    })

  await Promise.all(promises)
}

export const generateAndSendSummary = async (args: GenerateSummaryArgs) => {
  const { summary, base64Docx, summaryTitle, subjectDate } = await generateSummary(args)
  await sendSummaryEmail({
    summary,
    base64Docx,
    summaryTitle,
    subjectDate,
    receivers: args.receivers,
    subject: args.subject,
    resourceInInsightEngineId: args.resourceInInsightEngineId,
  })
  return summary
}