import { log } from "@/services/logger";
import {
  aidaEndpoint,
  DATA_RETENTION_DAYS,
  DATA_RETENTION_REMINDER_DAYS,
} from "@/config";
import { sendRetentionReminderEmail } from "@/services/email";
import { Op } from "sequelize";
import { subDays, addDays } from "date-fns";
import { models } from "@/schemas";
import { RetentionSetting } from "@/schemas/retention/RetentionSetting.model";
import { Session } from "@/schemas/session/Session.model";
import { Resource } from "@/schemas/resource/Resource.model";
import { getFirebaseAuth } from "../firebase";

// Testing configuration
const TEST_CONFIG = {
  enabled: process.env.NODE_ENV === "development",
  retentionDays: 1, // 1 day retention for testing
  reminderDays: 0.5, // 12 hours before deletion
};

export type ResourceWithAssociations = Resource & {
  retentionSetting: RetentionSetting;
  session: Session;
};

export class RetentionService {
  private getRetentionDays(): number {
    return TEST_CONFIG.enabled
      ? TEST_CONFIG.retentionDays
      : DATA_RETENTION_DAYS;
  }

  private getReminderDays(): number {
    return TEST_CONFIG.enabled
      ? TEST_CONFIG.reminderDays
      : DATA_RETENTION_REMINDER_DAYS;
  }

  /**
   * Find resources that are due for deletion
   */
  private async findResourcesForDeletion(): Promise<
    ResourceWithAssociations[]
  > {
    const cutoffDate = new Date();

    const retentionSettings = await models.RetentionSetting.xFind({
      expiryDate: {
        [Op.lt]: cutoffDate,
      },
    });

    const resources = await models.Resource.xFind({
      [Op.or]: [
        {
          id: {
            [Op.in]: retentionSettings.map((setting) => setting.resourceId),
          },
        },
      ],
    });

    const resourcesWithAssociations = await Promise.all(
      resources.map(async (resource) => {
        const retentionSetting = retentionSettings.find(
          (setting) => setting.resourceId === resource.id
        );
        const session = await models.Session.xFind1By("id", resource.sessionId);
        return {
          ...resource,
          retentionSetting,
          session,
        };
      })
    );

    return resourcesWithAssociations;
  }

  /**
   * Find resources that need reminder emails
   */
  private async findResourcesForReminder(): Promise<
    ResourceWithAssociations[]
  > {
    const reminderDate = new Date();
    const retentionDays = this.getRetentionDays();
    const reminderDays = this.getReminderDays();

    const retentionSettings = await models.RetentionSetting.xFind({
      [Op.or]: [
        // Resources with no custom retention date approaching default retention period
        {
          expiryDate: null,
          createdAt: {
            [Op.lt]: subDays(reminderDate, retentionDays - reminderDays),
            [Op.gt]: subDays(reminderDate, retentionDays - reminderDays - 1),
          },
        },
        // Resources with custom retention date approaching expiry
        {
          expiryDate: {
            [Op.lt]: addDays(reminderDate, reminderDays),
            [Op.gt]: reminderDate,
          },
        },
      ],
    });

    const resources = await models.Resource.xFind({
      id: {
        [Op.in]: retentionSettings.map((setting) => setting.resourceId),
      },
    });

    const resourcesWithAssociations = await Promise.all(
      resources.map(async (resource) => {
        const retentionSetting = retentionSettings.find(
          (setting) => setting.resourceId === resource.id
        );
        const session = await models.Session.xFind1By("id", resource.sessionId);
        return {
          ...resource,
          retentionSetting,
          session,
        };
      })
    );

    return resourcesWithAssociations;
  }

  /**
   * Send reminder emails for resources that will be deleted soon
   */
  private async sendReminderEmails() {
    const resources = await this.findResourcesForReminder();
    const retentionDays = this.getRetentionDays();

    for (const resource of resources) {
      const auth = await getFirebaseAuth();
      const user = await auth.getUser(resource.session.createdById);
      if (user) {
        try {
          const expiryDate =
            resource.retentionSetting?.expiryDate ||
            new Date(
              resource.createdAt.getTime() + retentionDays * 24 * 60 * 60 * 1000
            );

          const session = await models.Session.xFind1ById(resource.sessionId);

          await sendRetentionReminderEmail({
            to: user.email,
            subject: "Your meeting data will be deleted soon",
            substitutions: {
              meetingTitle: session.title,
              userName: user.displayName,
              deletionDate: expiryDate.toLocaleDateString(),
              daysRemaining: this.getReminderDays().toString(),
              extendUrl: `${aidaEndpoint}/accept-project-invitation?resourceId=${resource.id}`,
            },
          });
          log.info(`Sent retention reminder email for resource ${resource.id}`);
        } catch (error) {
          log.error(
            `Failed to send retention reminder email for resource ${resource.id}:`,
            error
          );
        }
      }
    }
  }

  /**
   * Delete resources that have exceeded the retention period
   */
  private async deleteExpiredResources() {
    const resources = await this.findResourcesForDeletion();

    for (const resource of resources) {
      try {
        // Delete associated data first
        await models.Summary.xDestroyBy("resourceId", resource.id);

        const resourceInInsightEngine =
          await models.ResourceInInsightEngine.xFind1By(
            "resourceId",
            resource.id
          );
        if (resourceInInsightEngine) {
          await models.Transcription.xDestroy({
            resourceInInsightEngineId: resourceInInsightEngine.id,
          });
          await models.ResourceInInsightEngine.xDestroyById(
            resourceInInsightEngine.id
          );
        }

        // Delete retention settings
        if (resource.retentionSetting) {
          await models.RetentionSetting.xDestroyById(
            resource.retentionSetting.id
          );
        }

        // Delete the resource
        await models.Resource.xDestroyById(resource.id);
        log.info(`Deleted expired resource ${resource.id}`);
      } catch (error) {
        log.error(`Failed to delete expired resource ${resource.id}:`, error);
      }
    }
  }

  /**
   * Extend the retention period for a resource
   */
  public async extendRetention(resourceId: string, extensionDays: number) {
    const resource = await models.Resource.xFind1ById(resourceId);
    const retentionSetting = await models.RetentionSetting.xFind1By(
      "resourceId",
      resourceId
    );

    if (!resource) {
      throw new Error("Resource not found");
    }
    if (!retentionSetting) {
      throw new Error("Retention setting not found");
    }

    const currentExpiryDate =
      retentionSetting.expiryDate ||
      new Date(
        resource.createdAt.getTime() + DATA_RETENTION_DAYS * 24 * 60 * 60 * 1000
      );

    const newExpiryDate = addDays(currentExpiryDate, extensionDays);

    // Create or update retention settings
    if (retentionSetting) {
      await models.RetentionSetting.xUpdateById(retentionSetting.id, {
        expiryDate: newExpiryDate,
        extensionCount: retentionSetting.extensionCount + 1,
        lastExtensionDate: new Date(),
        extensionHistory: [
          ...(retentionSetting.extensionHistory as any[]),
          {
            date: new Date(),
            previousExpiryDate: currentExpiryDate,
            newExpiryDate,
            extensionDays,
          },
        ],
      });
    } else {
      await models.RetentionSetting.create({
        resourceId: resource.id,
        expiryDate: newExpiryDate,
        extensionCount: 1,
        lastExtensionDate: new Date(),
        extensionHistory: [
          {
            date: new Date(),
            previousExpiryDate: currentExpiryDate,
            newExpiryDate,
            extensionDays,
          },
        ],
      });
    }

    return resource;
  }

  /**
   * Run the retention service
   */
  public async run() {
    try {
      // First send reminders
      await this.sendReminderEmails();

      // Then delete expired resources
      await this.deleteExpiredResources();
    } catch (error) {
      log.error("Error running retention service:", error);
    }
  }
}
