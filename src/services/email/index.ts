import sendgrid from '@sendgrid/mail'
import { sendgridApiKey, sendgridFromName, sendgridFromEmail, webAppUrl } from '@/config'
import { MeetingType } from './template/confirmedMeeting'
import { generateSummaryTemplate, SummarySection } from './template/summary'
import { projectInvitationTemplate } from './template/projectInvitation'
import { projectAccessRequestTemplate } from './template/projectAccessRequest'
import { projectAccessRequestRejectedTemplate } from './template/projectAccessRequestRejected'
import { createLogger } from '@/services/logger'
import { SendgridTemplates } from './template'
import { projectAccessRequestApprovedTemplate } from './template/projectAccessRequestApproved'
import { getFirebaseUserByEmail } from '../firebase'

if (sendgridApiKey) { sendgrid.setApiKey(sendgridApiKey) }

const logger = createLogger('EmailService')

export interface SendMailParams {
    to: string | string[]
    cc?: string | string[]
    subject: string
    text?: string
    html?: string
    attachments?: Array<Partial<{
        content: string
        filename: string
        type: string
        disposition: 'attachment' | 'inline'
    }>>
    substitutions?: Record<string, string>
    dynamicTemplateData?: Record<string, any>
}

export type SendSendMailParams = {
    html?: string
    templateId?: string
}

const sendEmail = async (options: SendMailParams, sendOptions: SendSendMailParams): Promise<boolean> => {
    try {
        logger.info(`Sending email with options: ${JSON.stringify({
            to: options.to,
            cc: options.cc,
            subject: options.subject,
            text: options.text,
            hasHTML: options.html !== undefined,
            hasAttachments: options.attachments !== undefined,
        })}`)

        const msg = {
            personalizations: [{
                to: Array.isArray(options.to) ? options.to.map(email => ({ email })) : [{ email: options.to }],
                cc: Array.isArray(options.cc) ? options.cc.map(email => ({ email })) : options.cc ? [{ email: options.cc }] : undefined,
            }],
            from: {
                name: sendgridFromName,
                email: sendgridFromEmail,
            },
            subject: options.subject,
            text: options.text,
            ...sendOptions,
            ...(sendOptions.templateId ? { dynamicTemplateData: options.dynamicTemplateData } : {substitutions: options.substitutions, substitutionWrappers: ['{{', '}}']}),
        }

        await sendgrid.send(msg)
        logger.info('Email sent successfully', { to: options.to, subject: options.subject, msg })
        return true
    } catch (error) {
        logger.error('Error sending email:', error)
        return false
    }
}

export const sendVerificationEmail = async (params: SendMailParams): Promise<boolean> => {
    return sendEmail({
        to: params.to,
        subject: params.subject,
        cc: params.cc,
        attachments: params.attachments,
        dynamicTemplateData: params.substitutions,
    }, {
        templateId: SendgridTemplates.AIDA_GUEST_VERIFICATION,
    })
}

export const sendConfirmedMeetingEmail = async (params: SendMailParams, type: MeetingType): Promise<boolean> => {
    return sendEmail({
        to: params.to,
        subject: params.subject,
        cc: params.cc,
        attachments: params.attachments,
        dynamicTemplateData: params.substitutions,
    }, {
        templateId: type === MeetingType.Existed ? SendgridTemplates.AIDA_CONFIRMED_MEETING : SendgridTemplates.AIDA_GUEST_JOINING_CONFIRMATION,
    })
}

export const sendSessionDeletedEmail = async (params: SendMailParams): Promise<boolean> => {
    return sendEmail({
        to: params.to,
        subject: params.subject,
        cc: params.cc,
        attachments: params.attachments,
        dynamicTemplateData: params.substitutions,
    }, {
        templateId: SendgridTemplates.AIDA_SESSION_DELETED,
    })
}

export const sendSessionUpdatedEmail = async (params: SendMailParams): Promise<boolean> => {
    return sendEmail({
        to: params.to,
        subject: params.subject,
        cc: params.cc,
        attachments: params.attachments,
        dynamicTemplateData: params.substitutions,
    }, {
        templateId: SendgridTemplates.AIDA_SESSION_UPDATED,
    })
}


export const sendSummary = async (params: SendMailParams, sections: SummarySection[]): Promise<boolean> => {
    return sendEmail({
        to: params.to,
        subject: params.subject,
        cc: params.cc,
        attachments: params.attachments,
        substitutions: params.substitutions,
    }, {
        html: generateSummaryTemplate(sections),
    })
}

interface SendInvitationEmailPayload {
    to: string
    projectName: string
    code: string
    projectId: string
    message?: string
    invitedByUserEmail?: string
    projectOwnerName?: string
    recipientName?: string
}

export const sendProjectInvitationEmail = async (payload: SendInvitationEmailPayload): Promise<boolean> => {
    const { to, projectName, code, projectId, message, invitedByUserEmail } = payload
    logger.info('Sending invitation email', { to, projectName, code, message })

    const invitationLink = `${webAppUrl}/accept-project-invitation?code=${code}&projectId=${projectId}`
    const subject = `Invitation to collaborate on project: ${projectName} on AIDA`

    const [getSenderUserResult, getRecipientUserResult] = await Promise.all([
        getFirebaseUserByEmail(invitedByUserEmail),
        getFirebaseUserByEmail(to)
    ])

    const projectOwnerName = getSenderUserResult?.displayName
    const recipientName = getRecipientUserResult?.displayName

    return sendEmail({
        to,
        subject,
        cc: invitedByUserEmail ? [invitedByUserEmail] : undefined,
    }, {
        html: projectInvitationTemplate({ projectName, invitationLink, projectOwnerName, recipientName, projectOwnerEmail: invitedByUserEmail}),
    })
}

interface SendProjectAccessRequestEmailPayload {
    requesterEmail: string
    approverEmail: string
    projectName: string
    projectId: string
    requestId: string
    requesterName: string
    approverName: string
}

export const sendProjectAccessRequestEmail = async (payload: SendProjectAccessRequestEmailPayload): Promise<boolean> => {
    const { approverEmail, projectName, projectId, requestId, requesterEmail } = payload
    logger.info(`Sending project access request email to ${approverEmail} for project ${projectName}`)

    const [getRequesterUserResult, getApproverUserResult] = await Promise.all([
        getFirebaseUserByEmail(requesterEmail),
        getFirebaseUserByEmail(approverEmail)
    ])

    const requesterName = getRequesterUserResult?.displayName
    const approverName = getApproverUserResult?.displayName
    return sendEmail({
        to: approverEmail,
        subject: `Access request to project: ${projectName} on AIDA`,
    }, {
        html: projectAccessRequestTemplate({ requesterEmail, projectName, projectId, requestId, requesterName, approverName }),
    })
}

interface SendProjectAccessRequestRejectedEmailPayload {
    projectName: string
    requesterEmail: string
    rejectedReason: string
}

export const sendProjectAccessRequestRejectedEmail = async (payload: SendProjectAccessRequestRejectedEmailPayload): Promise<boolean> => {
    const { projectName, requesterEmail, rejectedReason } = payload
    return sendEmail({
        to: requesterEmail,
        subject: `Project access request rejected: ${projectName} on AIDA`,
    }, {
        html: projectAccessRequestRejectedTemplate({ requesterEmail, projectName, rejectedReason }),
    })
}

interface SendProjectAccessRequestApprovedEmailPayload {
    projectName: string
    requesterEmail: string
    projectId: string
}

export const sendProjectAccessRequestApprovedEmail = async (payload: SendProjectAccessRequestApprovedEmailPayload): Promise<boolean> => {
    const { projectName, requesterEmail, projectId } = payload
    return sendEmail({
        to: requesterEmail,
        subject: `Project access request approved: ${projectName} on AIDA`,
    }, {
        html: projectAccessRequestApprovedTemplate({ projectName, projectId }),
    })
}


export const sendMeetingDeletedEmail = async (params: SendMailParams): Promise<boolean> => {
    return sendEmail({
        to: params.to,
        subject: params.subject,
        dynamicTemplateData: params.substitutions,
    }, {
        templateId: SendgridTemplates.MEETING_DELETED,
    })
}

export const sendRetentionReminderEmail = async (params: SendMailParams & {
  substitutions: {
    meetingTitle: string;
    deletionDate: string;
    daysRemaining: string;
    extendUrl: string;
  }
}) => {
  return sendEmail({
    to: params.to,
    subject: params.subject,
    cc: params.cc,
    attachments: params.attachments,
    dynamicTemplateData: params.substitutions,
  }, {
    templateId: SendgridTemplates.RETENTION_REMINDER,
  })
}
