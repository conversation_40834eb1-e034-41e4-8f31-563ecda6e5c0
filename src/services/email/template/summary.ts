
export type SummarySection = {
    type: 'text' | 'list'
    title: string
    content: string[]
    contentStyle?: string
    wrapperStyle?: string
}

export const sectionGenerator = (section: SummarySection): string => {
    const wrapperStyle = `font-weight: 400; margin: 0; padding-top: 8px;${section.wrapperStyle || ''}`
    const contentStyle = section.contentStyle || ''

    const content = section.type === 'text'
        ? `<p style="${wrapperStyle}">${section.content.join('')}</p>`
        : `<ul style="${wrapperStyle}">${section.content.map(item => `<li style="${contentStyle}">${item}</li>`).join('')}</ul>`

    return `
        <tr>
            <td>
                <label>${section.title}</label>
                ${content}
            </td>
        </tr>`
}

export const generateSummaryTemplate = (sections: SummarySection[]): string =>
    `<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
</head>

<body style="background-color: #fff;">
    <div
        style="max-width: 650px; margin: auto;font-family: 'Open Sans','Helvetica Neue',Helvetica,Arial,sans-serif;font-size: 14px;line-height: 20px;color: #181C22;font-weight: 400; background-color: #fff;">
        <link rel="preconnect" href="https://fonts.googleapis.com">
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link
            href="https://fonts.googleapis.com/css2?family=Open+Sans:ital,wght@0,300..800;1,300..800&family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap"
            rel="stylesheet">
        <table style="width: 100%;margin: 0 auto; border-spacing: 0 24px; border-collapse: separate;">
            <tr style="font-size: 14px;font-weight: 400;">
                <td>
                    Hi {{username}},
                </td>
            </tr>
            <tr style="font-size: 14px;">
                <td>
                    Here are your Smart Notes from the meeting:
                </td>
            </tr>
            ${sections.map(sectionGenerator).join('')}
            <tr>
                <td>
                    <p style="font-weight: 400; margin: 0; padding-top: 8px;">
                        📄 Full transcript: <a href="{{transcriptLink}}" style="text-decoration:none; font-size:14px;color: #006dcc;">{{transcriptLink}}</a>
                    </p>
                     <p style="font-weight: 400; margin: 0; padding-top: 8px;">
                        🔗 Manage sharing: <a href="{{manageSharingLink}}" style="text-decoration:none; font-size:14px;color: #006dcc;">{{manageSharingLink}}</a>
                    </p>
                </td>
            </tr>
            <tr>
                <td>
                    <p style="font-weight: 400; margin: 0; padding-top: 8px;">
                        Let me know if you’d like to adjust anything.
                    </p>
                     <p style="font-weight: 400; margin: 0; padding-top: 8px;">
                        — Aida
                    </p>
                </td>
            </tr>
             <tr style="margin-top: 14px;">
                <td>
                    <p style="font-weight: 400; margin: 0; padding-top: 8px;">
                        Aida @ Beings
                    </p>
                     <p style="font-weight: 400; margin: 0; padding-top: 8px;">
                        <EMAIL> 
                    </p>
                </td>
            </tr>
            <tr style="margin-top: 14px;">
                <td>
                    <p style="font-weight: 400; margin: 0; padding-top: 8px;">
                        This message and any attachments are private and confidential. If you have received this message in error, please notify us and remove it from your system. If you are not the intended recipient, please note that any form of distribution, copying or use of this message or the information in it is strictly prohibited and may be unlawful.
                    </p>
                     <p style="font-weight: 400; margin: 0; margin-top: 14px;">
                        We are GDPR, ISO 27001, Soc 2 Type 2 and HIPAA compliant.
                    </p>
                </td>
            </tr>
        </table>
        <div style="display: block; height: 1px; border: 0; border-top: 1px solid #C1C6D4;"></div>
    </div>
</body>
</html>`