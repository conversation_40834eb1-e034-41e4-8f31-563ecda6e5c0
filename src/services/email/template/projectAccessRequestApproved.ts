import { webAppUrl } from '@/config'

interface ProjectAccessRequestApprovedTemplateParams {
    projectName: string
    projectId: string
}

export const projectAccessRequestApprovedTemplate = ({ projectName, projectId }: ProjectAccessRequestApprovedTemplateParams): string => `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width,initial-scale=1">
</head>
<body>
<div>
    Hi,<br /><br />

    Your access request to ${projectName} on AIDA has been approved.<br /><br />

    Click to access: <a href="${webAppUrl}/project/${projectId}">${projectName}</a><br /><br />

    Thank you.<br /><br />

    —<br />
    Aida<br />
    <EMAIL><br /><br />

    Beings is GDPR, ISO 27001, SOC 2 Type II, and HIPAA compliant.
</div>
</body>
</html>
` 