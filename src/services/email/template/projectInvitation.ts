
interface ProjectInvitationTemplateParams {
    projectName: string
    invitationLink: string
    message?: string
    projectOwnerName?: string
    recipientName?: string
    projectOwnerEmail?: string
}

export const projectInvitationTemplate = ({ 
    projectName, 
    invitationLink, 
    projectOwnerName = "Project owner",
    recipientName = "there",
    projectOwnerEmail
}: ProjectInvitationTemplateParams): string => `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width,initial-scale=1">
</head>
<body>
<div>
    Hi ${recipientName},<br /><br />

    <a href="${projectOwnerEmail}">${projectOwnerName}</a> has invited you to join their project.<br /><br />

    Click to join: <a href="${invitationLink}">${projectName}</a><br /><br />

    Thank you.<br /><br />

    —<br />
    Aida<br />
    <EMAIL><br /><br />

    Beings is GDPR, ISO 27001, SOC 2 Type II, and HIPAA compliant.
</div>
</body>
</html>
` 