import { webAppUrl } from '@/config'

interface ProjectAccessRequestTemplateParams {
    requesterEmail: string
    projectName: string
    projectId: string
    requestId: string
    requesterName: string
    approverName: string
}

export const projectAccessRequestTemplate = ({ requesterEmail, projectName, projectId, requestId, requesterName, approverName }: ProjectAccessRequestTemplateParams): string => `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width,initial-scale=1">
</head>
<body>
<div>
    Hi ${approverName},<br /><br />

    <a href="mailto:${requesterEmail}">${requesterName}</a> has requested access to your project: <a href="${webAppUrl}/project/${projectId}">${projectName}</a> on AIDA<br /><br />

    Click to view: <a href="${webAppUrl}/project/${projectId}#sharing">${projectName}</a><br /><br />

    Thank you.<br /><br />

    —<br />
    Aida<br />
    <EMAIL><br /><br />

    Beings is GDPR, ISO 27001, SOC 2 Type II, and HIPAA compliant.
</div>
</body>
</html>
` 