import type { Options as SequelizeOptions } from "sequelize";
import S, { Sequelize } from "sequelize";

import type { SimpleLog } from "@/services/logger";
import type { Db, Options as PrivateOptions } from "./Db";
import { extendDb } from "./extendDb";
import { logging } from "./logging";

export * from "sequelize";

export type Options = PrivateOptions & {
  logging?: boolean;
  benchmark?: boolean;
  log?: SimpleLog;
} & Required<
    Pick<
      SequelizeOptions,
      "dialect" | "host" | "username" | "password" | "database"
    >
  >;

export const createNodejsDb = (options: Options) => {
  if (!["mysql", "mariadb", "sqlite", "postgres"].includes(options.dialect)) {
    throw new Error(`Dialect ${options.dialect} is not supported`);
  }
  const db = new Sequelize({
    ...options,
    define: {
      freezeTableName: true,
      timestamps: true,
      updatedAt: false,
      paranoid: false,
    },
    logging: logging(options),
    benchmark: options.benchmark,
  }) as Db;

  extendDb(S, db, options);

  return db;
};
