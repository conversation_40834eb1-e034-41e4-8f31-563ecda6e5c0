import { posthog<PERSON>ey, posthogHost } from "@/config";
import { PostHog } from "posthog-node";

if (!posthogKey) {
  throw new Error("PostHog API key is required");
}

// Initialize PostHog client
const posthog = new PostHog(posthogKey, {
  host: posthogHost || "https://us.i.posthog.com",
  flushAt: 20, // Number of events to queue before sending
  flushInterval: 10000, // Flush interval in milliseconds
});

// Define event names for type safety
export const EVENTS = {
  USER_IDENTIFIED: "user_identified",
  FEATURE_FLAG_CHECKED: "feature_flag_checked",
  SUMMARY_FEEDBACK_DETAILED: "summary_feedback_detailed"
  // Add more event names here
} as const;

type EventName = (typeof EVENTS)[keyof typeof EVENTS];

// Utility function to identify a user
export const identifyUser = (
  userId: string,
  properties?: Record<string, any>
) => {
  try {
    posthog.identify({
      distinctId: userId,
      properties,
    });
  } catch (error) {
    console.error("Failed to identify user in PostHog:", error);
  }
};

// Utility function to capture an event
export const captureEvent = (
  userId: string,
  eventName: EventName,
  properties?: Record<string, any>
) => {
  try {
    posthog.capture({
      distinctId: userId,
      event: eventName,
      properties,
    });
  } catch (error) {
    console.error(`Failed to capture event ${eventName} in PostHog:`, error);
  }
};

// Utility function to capture a group event
export const captureGroupEvent = (
  userId: string,
  groupType: string,
  groupKey: string,
  eventName: EventName,
  properties?: Record<string, any>
) => {
  try {
    posthog.capture({
      distinctId: userId,
      event: eventName,
      properties,
      groups: {
        [groupType]: groupKey,
      },
    });
  } catch (error) {
    console.error(
      `Failed to capture group event ${eventName} in PostHog:`,
      error
    );
  }
};

// Shutdown function to flush events before server shutdown
export const shutdown = async (): Promise<void> => {
  try {
    await posthog.shutdown();
  } catch (error) {
    console.error("Failed to shutdown PostHog client:", error);
  }
};

// Export the PostHog client instance
export default posthog;
