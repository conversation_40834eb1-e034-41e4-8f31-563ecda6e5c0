import { models } from "@/schemas";
import { createLogger } from "@/services/logger";
import { transcoderService } from "@/services/transcoder";
import { createTranscodingPayload } from "@/utils/transcoding";
import { gcsBucket, gcsResourceFolder } from "@/config";
import { TranscodingMode } from "@/schemas/transcoded/ResourceTranscodedJob.model";
import { ApiResponse, createSuccessResponse, createErrorResponse } from "@/utils/response";
import { GCS_URI_PREFIX } from "@/constants/storage";

const logger = createLogger("ResourceTranscodedJobService");

/**
 * Interface for transcoding job creation response
 */
interface TranscodingJobResponse extends ApiResponse {
  jobId?: string;
}

/**
 * Interface for transcoding job updates
 */
interface TranscodingJobUpdate {
  jobId?: string;
  callback_data?: any;
  retryCount?: number;
  retryReason?: string;
  hasAudio?: boolean;
  status?: string;
}

/**
 * Create a transcoding job for a file
 * @param filePath Path to the file to transcode
 * @param transcodedFileName Name for the transcoded file
 * @param resourceId Optional ID of the resource being transcoded
 * @param mode Transcoding mode (keep both or delete original)
 * @returns Response with job details
 */
export const createTranscodingJob = async (
  filePath: string,
  transcodedFileName: string,
  resourceId?: string,
  mode?: TranscodingMode
): Promise<TranscodingJobResponse> => {
  try {
    // Validate inputs
    if (!filePath) {
      return createErrorResponse('Input file path is required', 400);
    }
    
    if (!transcodedFileName) {
      return createErrorResponse('Output file name is required', 400);
    }
    
    // Full GCS URI of the source file to transcode
    const sourceGcsUri = `${GCS_URI_PREFIX}${gcsBucket}/${filePath}`;

    // GCS URI of the destination folder where transcoded files will be stored
    const destinationFolderGcsUri = `${GCS_URI_PREFIX}${gcsBucket}/${gcsResourceFolder}/`;

    // Relative path inside the bucket for the transcoded file
    const destinationRelativePath = `${gcsResourceFolder}/${transcodedFileName}`;

    // Create transcoding config with audio
    const config = createTranscodingPayload(
      sourceGcsUri,
      destinationFolderGcsUri,
      transcodedFileName,
      true
    );

    // Create the transcoding job
    const job = await transcoderService.createJob(
      sourceGcsUri,
      destinationFolderGcsUri,
      config,
    );

    // Extract job ID from the job name
    const jobId = job.name?.split("/").pop() || "";

    logger.info(
      `Creating ResourceTranscodedJob record`,
      {
        jobId,
        input: sourceGcsUri,
        output: destinationFolderGcsUri,
        resource: resourceId,
      }
    );

    // Create a record in the database
    const transcodingJob = await models.ResourceTranscodedJob.create({
      jobId,
      input: sourceGcsUri,
      output: destinationFolderGcsUri,
      outputFileName: transcodedFileName,
      resourceId,
      mode: mode || TranscodingMode.KEEP_BOTH,
      hasAudio: true,
      retryCount: 0,
    });

    return createSuccessResponse({
      jobId,
      job,
      transcodingJob,
    }, 'Transcoding job created successfully', 201);
  } catch (error) {
    logger.error(`Failed to create transcoding job: ${error.message}`, error);
    
    return createErrorResponse(
      `Failed to create transcoding job: ${error instanceof Error ? error.message : 'Unknown error'}`,
      500,
      error
    );
  }
};

/**
 * Find a transcoding job by metadata
 * @param metadata Key-value pairs to search for
 * @returns Transcoding job if found
 */
export const getTranscodingJobByJobMeta = async (metadata: Record<string, string>): Promise<ApiResponse> => {
  try {
    // This is a placeholder - in a real implementation, you would need to
    // find the right way to query jobs by resource ID or other metadata
    const job = await models.ResourceTranscodedJob.findOne({
      where: metadata,
    });

    if (!job) {
      return createErrorResponse('Transcoding job not found', 404);
    }

    return createSuccessResponse(job, 'Transcoding job found');
  } catch (error) {
    logger.error(`Failed to get transcoding job: ${error instanceof Error ? error.message : 'Unknown error'}`, error);

    return createErrorResponse(
      'Failed to get transcoding job',
      500,
      error
    );
  }
};

/**
 * Find a transcoding job by its ID
 * @param jobId ID of the job to find
 * @returns Transcoding job if found
 */
export const getTranscodingJobById = async (jobId: string): Promise<ApiResponse> => {
  try {
    if (!jobId) {
      return createErrorResponse('Job ID is required', 400);
    }
    
    const job = await models.ResourceTranscodedJob.findOne({
      where: {
        jobId,
      },
    });

    if (!job) {
      return createErrorResponse('Transcoding job not found', 404);
    }

    return createSuccessResponse(job, 'Transcoding job found');
  } catch (error) {
    logger.error(`Failed to get transcoding job: ${error instanceof Error ? error.message : 'Unknown error'}`, error);

    return createErrorResponse(
      'Failed to get transcoding job', 
      500,
      error
    );
  }
};

/**
 * Update a transcoding job
 * @param jobId ID of the job to update
 * @param updates Fields to update
 * @returns Updated job if successful
 */
export const updateTranscodingJob = async (
  jobId: string,
  updates: TranscodingJobUpdate
): Promise<ApiResponse> => {
  try {
    if (!jobId) {
      return createErrorResponse('Job ID is required', 400);
    }
    
    const [updated] = await models.ResourceTranscodedJob.update(updates, {
      where: {
        jobId,
      },
      returning: true,
    });

    if (updated === 0) {
      return createErrorResponse('Transcoding job not found', 404);
    }

    const updatedJob = await models.ResourceTranscodedJob.findOne({
      where: {
        jobId,
      },
    });

    return createSuccessResponse(updatedJob, 'Transcoding job updated successfully');
  } catch (error) {
    logger.error(`Failed to update transcoding job: ${error instanceof Error ? error.message : 'Unknown error'}`, error);

    return createErrorResponse(
      'Failed to update transcoding job',
      500,
      error
    );
  }
};
