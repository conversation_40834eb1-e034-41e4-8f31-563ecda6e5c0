import express from "express";
import { recallSystemCalendarEmail } from "@/config";
import { models } from "@/schemas";
import { sendAnnouncementEmail } from "./calendar.handler";
import { UserAida } from "@/schemas/userAida/UserAida.model";
import { AppCalendarEvent, RecallCalendarDetail } from "@/types/recall";
import { UserRecord } from "firebase-admin/lib/auth/user-record";
import { sendConfirmationEmail } from "./calendar.handler";
import { getFirebaseAuth } from "@/services/firebase";
import { sendSessionUpdatedEmail } from "@/services/email";
import { scheduleBot } from "@/services/recall/utils/createBot";
import { sendSessionDeletedEmail } from "@/services/email";
import { recallAiClient, removeBotFromCall } from "@/services/recall";
import { SessionStatus } from "@/schemas/session/Session.model";
import { format, isSameDay } from "date-fns";
import { log } from "@/services/logger";
import { google } from "googleapis";
import moment from "moment-timezone";
import { RRule } from "rrule";

const AIDA_BOT_EMAILS = recallSystemCalendarEmail;

export type RecurringEventDetails = {
  recurrence: string[];
  event: AppCalendarEvent;
};

export const getRecurringEventDetails = async (
  event: AppCalendarEvent
): Promise<RecurringEventDetails> => {
  const calendar = await recallAiClient.v2_retrieveCalendarById(
    event.calendar_id
  );
  const recurringEvent = await getEventByIdForRecallCalendar(
    event.raw.recurringEventId,
    calendar
  );
  return {
    recurrence: recurringEvent.recurrence,
    event: event,
  };
};

export const getEventByIdForRecallCalendar = async (
  recurringEventId: string,
  recallCalendar: Omit<RecallCalendarDetail, "webhook_url">
) => {
  if (!recallCalendar) {
    throw new Error("Missing Recall calendar instance");
  }

  try {
    const { oauth_client_id, oauth_client_secret, oauth_refresh_token } =
      recallCalendar;
    const auth = new google.auth.OAuth2({
      clientId: oauth_client_id,
      clientSecret: oauth_client_secret,
    });
    auth.setCredentials({
      refresh_token: oauth_refresh_token,
    });
    const response = await google.calendar({ version: "v3", auth }).events.get({
      calendarId: "primary",
      eventId: recurringEventId,
    });
    return response.data;
  } catch (err) {
    log.stack(err);
    throw err;
  }
};

export const getRecurringDateMessage = (
  recurringEventDetails: RecurringEventDetails
) => {
  if (!recurringEventDetails) {
    return null;
  }

  const { recurrence, event } = recurringEventDetails;
  if (!recurrence) {
    return null;
  }

  const recurrenceRule = RRule.fromString(recurrence[0]);

  const recurrenceText = recurrenceRule.toText();

  const firstStartDateMoment = moment(event.start_time).tz(
    event.raw.start.timeZone || ""
  );
  const firstEndDateMoment = moment(event.end_time).tz(
    event.raw.end.timeZone || ""
  );

  const firstStartTime = `${firstStartDateMoment.format("h:mm A")}`;
  const firstStartTimezone = `${firstStartDateMoment.format("[(GMT] Z[)]")}`;

  const firstEndTime = `${firstEndDateMoment.format("h:mm A")}`;

  return `${recurrenceText} from ${firstStartTime} to ${firstEndTime} ${firstStartTimezone}`;
};

export const isUpdatingSession = async (event: AppCalendarEvent) => {
  const session = await models.Session.xFind1({
    eventMeetingId: event.id,
  });
  if (!session) {
    return null;
  }
  const isStartTimeChanged =
    new Date(session.startTime).getTime() !==
    new Date(event.start_time).getTime();
  const isEndTimeChanged =
    new Date(session.endTime).getTime() !== new Date(event.end_time).getTime();

  return isStartTimeChanged || isEndTimeChanged ? session : null;
};

export async function onRecurringEventChanged(
  recurringEventId: string,
  events: AppCalendarEvent[]
) {
  // if it is deleted, we should remove it from the database
  // check attendees not include aida bot
  const organizer =
    events[0].raw.attendees.find(
      ({ email, organizer }) => email !== AIDA_BOT_EMAILS && organizer
    ) || events[0].raw.organizer;
  await attendeeWithEventsHandler(organizer.email, events);
}

async function attendeeWithEventsHandler(
  attendeeEmail: string,
  events: AppCalendarEvent[]
) {
  // process attendee with if attendee is existed in the database => auto schedule bot
  // if not => add to the database then send email to verify
  const auth = await getFirebaseAuth();
  let fUser: UserRecord = await auth
    .getUserByEmail(attendeeEmail)
    .catch(() => null);
  let userAida: UserAida;
  if (fUser) {
    userAida = await models.UserAida.xFind1({
      userId: fUser.uid,
    });
    if ((userAida && userAida.isConfirmed) || fUser.emailVerified) {
      await recurringSessionHandler(fUser, events, true);
      return;
    } else if (userAida && !userAida.isConfirmed) {
      await sendConfirmationEmail(fUser, events[0].id);
    }
  } else {
    fUser = await auth.createUser({
      email: attendeeEmail,
      disabled: true,
      emailVerified: false,
    });
    // create user aida
    userAida = await models.UserAida.xCreate({
      userId: fUser.uid,
      isConfirmed: false,
    });

    await recurringSessionHandler(fUser, events);
  }
}

async function recurringSessionHandler(
  fU: UserRecord,
  events: AppCalendarEvent[],
  shouldSchedule = false
) {
  // is creating new
  const session = await models.Session.xFind1({
    createdById: fU.uid,
    meetingUrl: events[0].meeting_url,
  });
  if (!session) {
    const isCreatingNew = events.find((event) => !event.is_deleted);
    if (isCreatingNew && shouldSchedule) {
      // schedule all events and send email one email to user
      for (const event of events) {
        await scheduleBotToNewMeeting(event, fU);
      }
      const dateTime = await recurringDateMessage(events[0]);
      await sendAnnouncementEmail(fU, events[0], dateTime);
      return;
    }
    log.info(`No session found for ${events[0].meeting_url}`);
    return;
  }

  // is updating
  // isUpdatingAll when events including both is_deleted and !is_deleted
  const isUpdatingAll =
    events.filter((event) => event.is_deleted).length ===
    events.filter((event) => !event.is_deleted).length;
  const updateArray = await Promise.all(
    events.map(async (event) => await isUpdatingSession(event))
  );
  const isUpdatingOne = updateArray.find((event) => Boolean(event));

  // is deleting
  const isDeletingAll = events.every((event) => event.is_deleted);
  const isDeletingOne = events.filter((event) => event.is_deleted).length === 1;

  if (isDeletingAll) {
    // delete all sessions
    const sessions = await models.Session.xFind({
      createdById: fU.uid,
      meetingUrl: events[0].meeting_url,
    });
    for (const session of sessions) {
      await removeBotFromCall(session.recallBotId);
      await models.Session.xDestroy({
        createdById: fU.uid,
        eventMeetingId: session.eventMeetingId,
      });
    }

    await sendSessionDeletedEmail({
      to: fU.email,
      subject: `Session ${session.id} deleted because the event ${events[0].meeting_url} is deleted`,
      substitutions: {
        username: fU.displayName || fU.email,
        meetingTitle: events[0].raw.summary,
      },
    });
  } else if (isDeletingOne) {
    // delete one session
    await removeBotFromCall(session.recallBotId);
    await models.Session.xDestroy({
      createdById: fU.uid,
      eventMeetingId: events[0].meeting_url,
    });

    await sendSessionDeletedEmail({
      to: fU.email,
      subject: `Session ${session.id} deleted because the event ${events[0].meeting_url} is deleted`,
      substitutions: {
        username: fU.displayName || fU.email,
        meetingTitle: events[0].raw.summary,
      },
    });
  } else if (isUpdatingAll) {
    const sessions = await models.Session.xFind({
      createdById: fU.uid,
      meetingUrl: events[0].meeting_url,
    });
    for (const session of sessions) {
      await removeBotFromCall(session.recallBotId);

      // reschedule the bot
      const prevEvent = events.find(
        (event) => event.id === session.eventMeetingId && event.is_deleted
      );
      // new event is the first event that is not deleted and same date but different time as prevEvent
      const newEvent = events.find(
        (event) =>
          !event.is_deleted &&
          isSameDay(new Date(event.start_time), new Date(prevEvent.start_time))
      );
      const bot = await scheduleBot(
        newEvent.id,
        newEvent.meeting_url,
        {},
        newEvent.start_time
      );
      await models.Session.xUpdateById(session.id, {
        startTime: new Date(newEvent.start_time),
        endTime: new Date(newEvent.end_time),
        recallBotId: bot.bot_id,
        eventMeetingId: newEvent.id,
      });
    }
    // send email to the user
    await sendSessionUpdatedEmail({
      to: fU.email,
      subject: `Session ${session.id} updated because the event ${events[0].meeting_url} is updated`,
      substitutions: {
        username: fU.displayName || fU.email,
        meetingTitle: events[0].raw.summary,
      },
    });
    log.info(
      `Send email to ${fU.email} for session ${session.id} updated because the event ${events[0].meeting_url} is updated`
    );
  } else if (isUpdatingOne) {
    // update one session
    const updatedSession = isUpdatingOne;
    await removeBotFromCall(updatedSession.recallBotId);
    const bot = await scheduleBot(
      events[0].id,
      events[0].meeting_url,
      {},
      events[0].start_time,
      fU.email
    );
    await models.Session.xUpdateById(updatedSession.id, {
      startTime: new Date(events[0].start_time),
      endTime: new Date(events[0].end_time),
      recallBotId: bot.bot_id,
    });

    // send email to the user
    await sendSessionUpdatedEmail({
      to: fU.email,
      subject: `Session ${session.id} updated because the event ${events[0].meeting_url} is updated`,
      substitutions: {
        username: fU.displayName || fU.email,
        meetingTitle: events[0].raw.summary,
      },
    });
  }
}

export const scheduleBotToNewMeeting = async (
  event: AppCalendarEvent,
  fU: UserRecord
) => {
  try {
    const bot = await scheduleBot(
      event.id,
      event.meeting_url,
      {},
      event.start_time,
      fU.email
    );
    const session = await models.Session.xCreate({
      recallBotId: bot.bot_id,
      title: `Meeting at ${format(new Date(), "dd MMM yyyy hh:mm a")}`,
      startTime: new Date(event.start_time),
      endTime: new Date(event.end_time),
      meetingUrl: event.meeting_url,
      createdById: fU.uid,
      status: SessionStatus.NotStarted,
      shouldSendSummaryToEmail: true,
      eventMeetingId: event.id,
    });
    return session;
  } catch (error) {
    log.error(
      `Error scheduling bot to new meeting ${event.meeting_url}: ${error}`
    );
    return null;
  }
};

const recurringDateMessage = async (event: AppCalendarEvent) => {
  const recurringEventDetails = await getRecurringEventDetails(event);
  return getRecurringDateMessage(recurringEventDetails);
};
