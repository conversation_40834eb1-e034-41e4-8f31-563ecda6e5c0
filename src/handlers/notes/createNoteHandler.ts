import express from "express";
import { models } from "@/schemas";
import { log } from "@/services/logger";
import { IntentionalError } from "@/utils/errors";

const createNoteHandler = async (
  req: express.Request,
  res: express.Response
) => {
  try {
    const { content, resourceId = null, title, projectId = null } = req.body;
    const currentUser = res.locals.user;

    if (!content || !title || (!resourceId && !projectId)) {
      res.status(400).json({ message: "Missing required fields" });
      return;
    }

    const note = await models.Note.xCreate({
      content,
      resourceId,
      projectId,
      title,
      createdById: currentUser.id,
      updatedById: currentUser.id,
    });

    // Fetch the created note with all relationships for frontend optimistic updates
    const createdNote = await models.Note.findByPk(note.id, {
      include: [
        {
          model: models.Resource,
          as: "resource",
          attributes: ["id", "name"],
        },
        {
          model: models.Project,
          as: "project",
          attributes: ["id", "name"],
        },
      ],
    });

    res.status(200).json(createdNote);
  } catch (error) {
    log.error(`[createNote] Failed to create note`, error);
    throw new IntentionalError("Failed to create note");
  }
};

export default createNoteHandler;
