import express from "express";
import { models } from "@/schemas";
import { log } from "@/services/logger";
import { IntentionalError } from "@/utils/errors";
import { Op } from "sequelize";

const getNotesHandler = async (req: express.Request, res: express.Response) => {
  try {
    const { resourceId, projectId } = req.query;
    const notes = await models.Note.findAll({
      where: {
        [Op.or]: [
          { resourceId: String(resourceId) },
          { projectId: String(projectId) },
        ],
      },
      order: [["createdAt", "DESC"]],
    });

    res.status(200).json(notes);
  } catch (error) {
    log.error(`[getNotesHandler] Failed to get notes`, error);
    throw new IntentionalError("Failed to get notes");
  }
};

export default getNotesHandler;
