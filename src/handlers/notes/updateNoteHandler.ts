import express from "express";
import { models } from "@/schemas";
import { log } from "@/services/logger";
import { IntentionalError } from "@/utils/errors";

const updateNoteHandler = async (
  req: express.Request,
  res: express.Response
) => {
  try {
    const { id } = req.params;
    const { content, title } = req.body;
    const currentUser = res.locals.user;

    if (!currentUser) {
      res.status(401).json({ error: "Unauthorized" });
      return;
    }

    // Update the note
    const [affectedRows] = await models.Note.update(
      {
        content,
        title,
        updatedById: currentUser.id,
      },
      {
        where: { id },
      }
    );

    if (affectedRows === 0) {
      res.status(404).json({ error: "Note not found" });
      return;
    }

    // Fetch and return the updated note with all fields
    const updatedNote = await models.Note.findByPk(id, {
      include: [
        {
          model: models.Resource,
          as: "resource",
          attributes: ["id", "name"],
        },
        {
          model: models.Project,
          as: "project",
          attributes: ["id", "name"],
        },
      ],
    });

    if (!updatedNote) {
      res.status(404).json({ error: "Note not found after update" });
      return;
    }

    res.status(200).json(updatedNote);
  } catch (error) {
    log.error("[updateNoteHandler] Failed to update note", error);
    throw new IntentionalError("Failed to update note");
  }
};

export default updateNoteHandler;
