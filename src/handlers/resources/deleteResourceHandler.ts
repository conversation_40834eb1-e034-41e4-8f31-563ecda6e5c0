import express from "express";
import { models } from "@/schemas";
import { log } from "@/services/logger";
import { IntentionalError } from "@/utils/errors";
import { deleteFile } from "@/services/storage";
import { getInsightEngineByResourceId } from "@/schemas/insightEngine/utils";
import { Op } from "sequelize";
import { isUserAccessibleResource } from "./resource.service";

const deleteResourceHandler = async (
  req: express.Request,
  res: express.Response,
) => {
  try {
    const { id: resourceId } = req.params;
    const currentUser = res.locals.user;

    const resource = await models.Resource.xFind1({
      id: resourceId,
    });

    if (!resource) {
      res.status(404).json({ message: "Resource not found" });
      return;
    }

    const { data: { canEdit } } = await isUserAccessibleResource(currentUser.id, resource);

    if (!canEdit) {
      res.status(403).json({ message: "You are not allowed to delete this resource" });
      return;
    }

    const { insightEngine, resourceInInsightEngine } =
      await getInsightEngineByResourceId(resourceId);

    await Promise.all([
      models.Resource.xDestroyById(resourceId),
      resourceInInsightEngine &&
      models.ResourceInInsightEngine.xDestroyById(resourceInInsightEngine.id),
      insightEngine && models.InsightEngine.xDestroyById(insightEngine.id),
      resourceInInsightEngine &&
      models.Transcription.xDestroyBy(
        "resourceInInsightEngineId",
        resourceInInsightEngine.id,
      ),
    ]);

    await Promise.all([
      deleteFile(resource.thumbnailUrl),
      deleteFile(resource.url),
    ]);

    res.status(200).json({ message: "Success", projectId: resource.projectId });
  } catch (error) {
    log.error(`Failed to delete resource: ${error.message}`);
    throw new IntentionalError("Failed to delete resource");
  }
};

export default deleteResourceHandler;
