import { Project } from "@/schemas/project/Project.model";
import { ProjectMember } from "@/schemas/project/ProjectMember.model";
import { Resource } from "@/schemas/resource/Resource.model";
import { Session } from "@/schemas/session/Session.model";

export interface ServiceResult<T> {
    success: boolean;
    message: string;
    statusCode: number;
    data: T;
}

export type IsUserEditableResourceResult = ServiceResult<{
    canView: boolean;
    canEdit: boolean;
}>

export type ResourceWithProjectMember = Resource & {
    project: Pick<Project, "id" | "name" | "createdById"> & {
        members: Pick<ProjectMember, "id" | "role" | "userId">[];
    };
};

export type ResourceWithSession = ResourceWithProjectMember & {
    session: Pick<Session, "id" | "status">;
};


