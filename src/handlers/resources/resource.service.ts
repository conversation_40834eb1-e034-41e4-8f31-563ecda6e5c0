import { models } from "@/schemas";
import ProjectMemberModel, {
  ProjectMember,
  ProjectMemberRole,
} from "@/schemas/project/ProjectMember.model";
import { Resource } from "@/schemas/resource/Resource.model";
import {
  IsUserEditableResourceResult,
  ResourceWithSession,
  ResourceWithProjectMember,
} from "./types";
import { createLogger } from "@/services/logger";
import { Project } from "@/schemas/project/Project.model";
import { Op } from "sequelize";
import { SessionStatus } from "@/schemas/session/Session.model";
import { getResourceData } from "@/schemas/resource/utils";

const log = createLogger("ResourceService");

export const getResourceById = async (resourceId: string, userId: string) => {
  try {
    const resourceQuery = await models.Resource.findOne({
      where: {
        id: resourceId,
      },
      attributes: {
        exclude: ["ffprobe"],
      },
      include: [
        {
          model: models.ProjectFolder,
          attributes: ["id", "name"],
          as: "folder",
        },
        {
          model: models.Project,
          attributes: ["id", "name", "createdById"],
          as: "project",
          required: false,
          include: [
            {
              model: models.ProjectMember,
              attributes: ["id", "role", "userId"],
              as: "members",
              where: {
                userId,
              },
              required: false,
            },
          ],
        },
      ],
    });

    const resource = resourceQuery?.toJSON() as ResourceWithProjectMember;

    if (!resource) {
      return {
        success: false,
        message: "Resource not found",
        statusCode: 404,
        data: null,
      };
    }

    const userPermissions = getResourceUserPermissions(resource, userId);

    return {
      success: true,
      message: "Resource found",
      statusCode: 200,
      data: {
        ...resource,
        userPermissions,
      },
    };
  } catch (error) {
    log.error(`Failed to get resource ${resourceId}`, error);
    return {
      success: false,
      message: "Failed to get resource",
      statusCode: 500,
      data: null,
    };
  }
};

/**
 * Get all resources that the user has access to
 *
 * There are 2 cases:
 * 1. The resource is owned by the user and belongs to no project => can view and edit
 * 2. The resource is in a project that the user is a member/owner of (getting from getUserAccessibleProjectIdsContext)=>
 *      - edit if the user is an editor
 *      - view only if the user is a viewer
 *
 * @param userId - The ID of the user to get resources for
 * @param accessibleProjectIds - The IDs of projects that the user has access to
 * @returns A list of resources that the user has access to
 */
export const getAllUserResources = async (
  userId: string,
  accessibleProjectIds: string[]
) => {
  try {
    const resources = await models.Resource.findAll({
      attributes: {
        exclude: ["ffprobe"],
      },
      where: {
        [Op.or]: [
          {
            createdById: userId,
            projectId: {
              [Op.or]: [{ [Op.is]: null }, ""],
            },
          }, // case 1
          { projectId: { [Op.in]: accessibleProjectIds } }, // case 2
        ],
      },
      include: [
        {
          model: models.Session,
          attributes: ["id", "status"],
          as: "session",
          required: false,
        },
        {
          model: models.Project,
          attributes: ["id", "name", "createdById"],
          as: "project",
          required: false,
          include: [
            {
              model: models.ProjectMember,
              attributes: ["id", "role", "userId"],
              as: "members",
              where: {
                userId,
              },
              required: false,
            },
          ],
        },
        {
          model: models.ProjectFolder,
          attributes: ["id", "name"],
          as: "folder",
          required: false,
        },
      ],
    });

    const output = await Promise.all(
      resources.map(async (r) => {
        const data = r.toJSON<ResourceWithSession>();
        const session = data.session;
        if (session && session.status !== SessionStatus.Completed) {
          return null;
        }

        const userPermissions = getResourceUserPermissions(data, userId);

        const resourceData = await getResourceData(data);

        return {
          ...resourceData,
          userPermissions,
        };
      })
    );

    // Sort by createdAt
    const sortedOutput = output.filter(Boolean).sort((a, b) => {
      return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
    });

    return {
      success: true,
      message: "Resources fetched successfully",
      statusCode: 200,
      data: sortedOutput,
    };
  } catch (error) {
    log.error(`Failed to get all user resources`, error);
    return {
      success: false,
      message: "Failed to get all user resources",
      statusCode: 500,
      data: [],
    };
  }
};
/**
 * Check if the user has edit permission for the resource
 * @param userId - The ID of the user to check
 * @param resource - The resource to check
 * @returns An object containing the result of the check
 *
 * There are 4 cases:
 *
 * 1. resource is owned by the user and belongs to no project => can view and edit
 *
 * 2. resource is in a project that the user is a editor of => can view and edit
 *
 * 3. resource is in a project that the user is a viewer of => can view but not edit
 *
 * 4. resource is in a project that the user is not a member of => can view only
 */
export async function isUserAccessibleResource(
  userId: string,
  resource: Resource
): Promise<IsUserEditableResourceResult> {
  const isResourceOwnedByUser = resource.createdById === userId;
  const isHaveNoProject = !resource.projectId;

  if (isResourceOwnedByUser && isHaveNoProject) {
    return {
      success: true,
      message: "Resource is owned by the user",
      statusCode: 200,
      data: {
        canView: true,
        canEdit: true,
      },
    };
  }

  const projectQuery = await models.Project.findOne({
    where: {
      id: resource.projectId,
    },
    include: [
      {
        model: ProjectMemberModel,
        required: false,
        attributes: ["id", "role", "userId"],
        where: {
          userId,
        },
        as: "members",
        on: {
          projectId: resource.projectId,
        },
      },
    ],
  });

  const project = projectQuery?.toJSON() as Project & {
    members: ProjectMember[];
  };

  if (!project) {
    return {
      success: false,
      message: "resource's project is not found",
      statusCode: 403,
      data: {
        canView: false,
        canEdit: false,
      },
    };
  }

  const isProjectOwner = project.createdById === userId;

  if (isProjectOwner) {
    return {
      success: true,
      message: "Resource's project is owned by the user",
      statusCode: 200,
      data: {
        canView: true,
        canEdit: true,
      },
    };
  }

  const projectMember = project.members?.find(
    (member) => member.userId === userId
  );

  if (!projectMember) {
    return {
      success: false,
      message: "User is not a member of the project",
      statusCode: 403,
      data: {
        canView: false,
        canEdit: false,
      },
    };
  }

  return {
    success: true,
    message: "Resource's project is owned by the user",
    statusCode: 200,
    data: {
      canView: true,
      canEdit: projectMember.role === ProjectMemberRole.EDITOR,
    },
  };
}

/**************************************************
 *             PRIVATE FUNCTIONS
 *
 ***************************************************/

/**
 * Get the user permissions for a resource
 * @param resource - The resource to get the user permissions for, Should include project and project members in SQL relations
 * @param userId - The ID of the user to get the permissions for
 * @returns An object containing the user permissions { canView: boolean, canEdit: boolean }
 */
function getResourceUserPermissions(
  resource: ResourceWithProjectMember,
  userId: string
) {
  const projectId = resource.projectId;
  const project = resource.project;
  const isProjectOwner = project?.createdById === userId;

  if (!project || !projectId || isProjectOwner) {
    return {
      canView: true,
      canEdit: true,
    };
  }

  const projectMember = project?.members.find(
    (member) => member.userId === userId
  );

  return {
    canView: true,
    canEdit: projectMember?.role === ProjectMemberRole.EDITOR,
  };
}

export const backfillUserResourcesWithDefaultProject = async (
  userId: string,
  projectId: string
) => {
  // 1. find all resources missing a project
  const resources = await models.Resource.findAll({
    where: {
      createdById: userId,
      projectId: {
        [Op.or]: [{ [Op.is]: null }, ""],
      },
    },
  });

  if (resources.length === 0) {
    return {
      success: true,
      message: "No resources to update",
      statusCode: 200,
    };
  }

  // 2. update the resources to have the default projectId
  try {
    await Promise.all(
      resources.map(async (resource) => {
        const resourceData = resource.toJSON();
        await models.Resource.update(
          {
            projectId,
          },
          {
            where: { id: resourceData.id },
          }
        );
      })
    );

    return {
      success: true,
      message: "Resources updated successfully",
      statusCode: 200,
    };
  } catch (error) {
    log.error(
      `Failed to update resources to have the default projectId`,
      error
    );
    return {
      success: false,
      message: "Failed to update resources to have the default projectId",
      statusCode: 500,
    };
  }
};

/**
 * Get the transcoding status for multiple resources by their IDs
 * @param resourceIds - Array of resource IDs to check
 * @returns Object containing success status and data with IDs and transcoding status
 */
export const getResourcesTranscodingStatus = async (resourceIds: string[]) => {
  try {
    // Validate input
    if (!resourceIds || !Array.isArray(resourceIds) || resourceIds.length === 0) {
      return {
        success: false,
        message: "No valid resource IDs provided",
        statusCode: 400,
        data: [],
      };
    }

    // Find resources with the given IDs
    // NOTE: To optimize this query, ensure that the 'isTranscoding' field is indexed in the database
    const resources = await models.Resource.findAll({
      where: {
        id: {
          [Op.in]: resourceIds
        }
      },
      attributes: ['id', 'isTranscoding', 'transcodedFileSize'], // Only select necessary fields
    });

    // Check if any resources were found
    if (resources.length === 0) {
      return {
        success: true,
        message: "No resources found with the provided IDs",
        statusCode: 200,
        data: [],
      };
    }

    return {
      success: true,
      message: "Resources transcoding status fetched successfully",
      statusCode: 200,
      data: resources.map(r => r.toJSON()),
    };
  } catch (error) {
    log.error(`Failed to get resources transcoding status: ${error instanceof Error ? error.message : 'Unknown error'}`, error);
    
    return {
      success: false,
      message: "Failed to get resources transcoding status",
      statusCode: 500,
      data: [],
      error: error instanceof Error ? error.message : String(error)
    };
  }
};
