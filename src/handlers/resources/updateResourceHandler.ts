import express from "express";
import { models } from "@/schemas";
import { log } from "@/services/logger";
import { IntentionalError } from "@/utils/errors";
import { getInsightEngineByResourceId } from "@/schemas/insightEngine/utils";
import { Resource } from "@/schemas/resource/Resource.model";
import { getResourceData } from "@/schemas/resource/utils";
import { Op } from "sequelize";
import { isUserAccessibleResource } from "./resource.service";

const updateResourceHandler = async (
  req: express.Request,
  res: express.Response
) => {
  try {
    const { id: resourceId } = req.params;
    const currentUser = res.locals.user;
    const payload = req.body as Partial<Resource>;

    const resource = await models.Resource.xFind1({
      id: resourceId,
    });

    if (!resource) {
      res.status(404).json({ message: "Resource not found" });
      return;
    }

    const isEditable = await isUserAccessibleResource(currentUser.id, resource);

    if (!isEditable.success || !isEditable.data.canEdit) {
      res.status(403).json({ message: isEditable.message });
      return;
    }


    const { insightEngine } = await getInsightEngineByResourceId(resourceId);
    const { name, ...rest } = payload;

    if (name) {
      await models.InsightEngine.xUpdateById(insightEngine.id, {
        name,
      });
    }

    await models.Resource.xUpdateById(resourceId, rest);

    const updatedResource = await models.Resource.xFind1({
      id: resourceId,
    });

    const updatedResourceData = await getResourceData(updatedResource);

    res.status(200).json(updatedResourceData);
  } catch (error) {
    log.error(`Failed to update resource: ${error.message}`);
    throw new IntentionalError("Failed to update resource");
  }
};

export default updateResourceHandler;
