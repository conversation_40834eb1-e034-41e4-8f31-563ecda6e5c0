import express from "express";
import { v4 as uuidv4 } from "uuid";
import { log } from "@/services/logger";
import { IntentionalError } from "@/utils/errors";
import { generateSignedUrlForUpload } from "@/services/storage";
import { gcsResourceFolder } from "@/config";
import path from "path";

const getResourceUploadUrlHandler = async (
  req: express.Request,
  res: express.Response
) => {
  try {
    const { fileName } = req.body;

    if (!fileName) {
      res.status(400).json({ message: "Missing file name" });
      return;
    }

    const { name, ext } = path.parse(fileName);
    const uploadFileName = `${name}-${uuidv4()}${ext}`;

    const data = await generateSignedUrlForUpload(
      uploadFileName,
      gcsResourceFolder
    );
    
    data.fields;

    res.status(200).json(data);
  } catch (error) {
    log.error(`Failed to generate upload URL: ${error.message}`);
    throw new IntentionalError("Failed to generate upload URL");
  }
};

export default getResourceUploadUrlHandler;
