import express, { response } from "express";
import { format } from "date-fns";
import { log } from "@/services/logger";
import { IntentionalError } from "@/utils/errors";
import { createBot } from "@/services/recall/utils/createBot";
import { models } from "@/schemas";
import { SessionStatus } from "@/schemas/session/Session.model";

const recordMeetingHandler = async (
  req: express.Request,
  res: express.Response
) => {
  try {
    const { url: meetingUrl, projectId } = req.body;
    const currentUser = res.locals.user;

    if (!meetingUrl) {
      res.status(400).json({ message: "Missing meeting URL" });
      return;
    }

    log.info(`[recordMeetingHandler] Start recording meeting ${meetingUrl}`);

    // Add bot to meeting
    const bot = await createBot(
      meetingUrl,
      {},
      new Date().toISOString(),
      currentUser.name
    );

    // Create session
    const session = await models.Session.xCreate({
      recallBotId: bot.id,
      title: `Meeting at ${format(new Date(), "dd MMM yyyy hh:mm a")}`,
      startTime: new Date(bot.join_at),
      meetingUrl,
      createdById: currentUser.id,
      status: SessionStatus.NotStarted,
      projectId,
    });

    // Fetch the created session with relationships for frontend optimistic updates
    const createdSession = await models.Session.findByPk(session.id, {
      include: [
        {
          model: models.Project,
          as: "project",
          attributes: ["id", "name"],
        },
      ],
    });

    log.info(`[recordMeetingHandler] Created session`, session);

    res.status(200).json(createdSession);
  } catch (error) {
    log.error(`[recordMeetingHandler] Failed to record meeting`, error);
    throw new IntentionalError("Failed to record meeting", error);
  }
};

export default recordMeetingHandler;
