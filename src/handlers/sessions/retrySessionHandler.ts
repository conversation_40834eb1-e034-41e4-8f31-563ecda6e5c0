import express from "express";
import { models } from "@/schemas";
import { log } from "@/services/logger";
import { IntentionalError } from "@/utils/errors";
import { SessionStatus } from "@/schemas/session/Session.model";
import { recallAiClient } from "@/services/recall";
import { BotChangeStatusCode } from "@/types/recall";
import handleBotDone from "@/services/recall/utils/handleBotDone";
import { updateSessionByBotId } from "@/schemas/session/utilts";

const retrySessionHandler = async (
  req: express.Request,
  res: express.Response
) => {
  try {
    const { id: sessionId } = req.params;
    const currentUser = res.locals.user;

    const session = await models.Session.xFind1({
      createdById: currentUser.id,
      id: sessionId,
    });

    if (!session) {
      res.status(404).json({ message: "Session not found" });
      return;
    }

    const { status, recallBotStatus, recallBotId } = session;

    if ([SessionStatus.NotStarted, SessionStatus.Completed].includes(status)) {
      res
        .status(400)
        .json({ message: "Session is either not started or completed" });
      return;
    }

    // Failed while processing, can retry
    if (
      status === SessionStatus.Failed &&
      recallBotStatus === BotChangeStatusCode.Done
    ) {
      await handleBotDone(recallBotId);
    }

    // Stuck in processing, can retry
    if (
      status === SessionStatus.Processing &&
      recallBotStatus === BotChangeStatusCode.Done
    ) {
      await updateSessionByBotId(recallBotId, {
        status: SessionStatus.InProgress,
      });
      await handleBotDone(recallBotId);
    }

    // Fetch and return the updated session
    const updatedSession = await models.Session.findByPk(sessionId, {
      include: [
        {
          model: models.Project,
          as: "project",
          attributes: ["id", "name"],
        },
      ],
    });

    if (!updatedSession) {
      res.status(404).json({ error: "Session not found after retry" });
      return;
    }

    res.status(200).json(updatedSession);
  } catch (error) {
    log.error(`Failed to delete session: ${error.message}`);
    throw new IntentionalError("Failed to delete session");
  }
};

export default retrySessionHandler;
