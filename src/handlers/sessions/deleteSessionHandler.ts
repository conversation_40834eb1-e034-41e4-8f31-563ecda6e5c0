import express from "express";
import { models } from "@/schemas";
import { log } from "@/services/logger";
import { IntentionalError } from "@/utils/errors";
import { SessionStatus } from "@/schemas/session/Session.model";
import { recallAiClient } from "@/services/recall";
import { sendMeetingDeletedEmail } from "@/services/email";

const deleteSessionHandler = async (
  req: express.Request,
  res: express.Response,
) => {
  try {
    const { id: sessionId } = req.params;
    const currentUser = res.locals.user;

    const session = await models.Session.xFind1({
      createdById: currentUser.id,
      id: sessionId,
    });

    if (!session) {
      res.status(404).json({ message: "Session not found" });
      return;
    }

    // If session is in progress, remove bot from call
    if (session.status === SessionStatus.InProgress && session.recallBotId) {
      await recallAiClient.removeBotFromCall(session.recallBotId);
    }

    if(session.shouldSendSummaryToEmail && currentUser.id === session.createdById) {
      // Remove existing summary
      const resource = await models.Resource.xFind1({
        sessionId: session.id,
      });
      if(resource) {
        await models.Summary.xDestroy({
          resourceId: resource.id,
        });
      }

      // Send email to
      await sendMeetingDeletedEmail({
        to: currentUser.email,
        subject: `${session.title} has been deleted`,
        substitutions: {
          meetingTitle: session.title,
          username: currentUser.name,
        }
      });
    }

    await models.Session.xDestroyById(sessionId);

    res.status(200).json({ message: "Success" });
  } catch (error) {
    log.error(`Failed to delete session: ${error.message}`);
    throw new IntentionalError("Failed to delete session");
  }
};

export default deleteSessionHandler;
