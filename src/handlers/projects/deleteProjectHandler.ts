import express from "express";
import { models } from "@/schemas";
import { log } from "@/services/logger";
import { IntentionalError } from "@/utils/errors";
import * as projectService from "@/services/project/project.service";

const deleteProjectHandler = async (
  req: express.Request,
  res: express.Response
) => {
  try {
    const { id: projectId } = req.params;
    const currentUser = res.locals.user;

    const defaultProject = await projectService.getProjectDefault(
      currentUser.id
    );

    const project = await models.Project.xFind1({
      id: projectId,

      createdById: currentUser.id,
    });

    if (!project) {
      res.status(404).json({ message: "Project not found" });
      return;
    }

    if (project.isDefault) {
      res.status(400).json({ message: "Default project cannot be deleted" });
      return;
    }

    await Promise.all([
      // Delete project
      models.Project.xDestroyById(projectId),
      // Delete project folders
      models.ProjectFolder.xDestroyBy("projectId", projectId),
      // Update resources
      models.Resource.xUpdateBy("projectId", projectId, {
        projectId: defaultProject.data?.id ?? null,
        folderId: null,
      }),
    ]);

    res.status(200).json({ message: "Success" });
  } catch (error) {
    log.error(`Failed to delete project: ${error.message}`);
    throw new IntentionalError("Failed to delete project");
  }
};

export default deleteProjectHandler;
