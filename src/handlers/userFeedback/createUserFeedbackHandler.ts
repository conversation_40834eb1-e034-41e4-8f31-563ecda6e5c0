import express from "express";
import { models } from "@/schemas";
import { log } from "@/services/logger";
import { IntentionalError } from "@/utils/errors";
import posthog, { EVENTS } from "@/services/posthog";

const createUserFeedbackHandler = async (
    req: express.Request,
    res: express.Response
) => {
    try {
        const { entityId, feedback, reason, type, rating } = req.body;
        const currentUser = res.locals.user;

        if (!entityId) {
            res.status(400).json({ message: "Missing resource id" });
            return;
        }

        const userFeedback = await models.UserFeedback.create({
            entityId,
            userId: currentUser.id,
            feedback,
            reason,
            type,
            rating,
        });

        // Track feature flag check
        posthog.capture({
            distinctId: currentUser.id,
            event: EVENTS.SUMMARY_FEEDBACK_DETAILED,
            properties: {
                feedback,
                entityId,
                reason,
                type,
                rating,
            },
        });

        res.status(201).json(userFeedback);
    } catch (error) {
        log.error(`[createUserFeedback] Failed to create user feedback`, error);
        throw new IntentionalError("Failed to create user feedback", error);
    }
};

export default createUserFeedbackHandler;