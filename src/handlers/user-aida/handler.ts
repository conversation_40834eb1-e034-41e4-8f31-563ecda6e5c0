import { models } from "@/schemas";
import { getFirebaseAuth } from "@/services/firebase";
import { recallAiClient } from "@/services/recall";
import { scheduleBot } from "@/services/recall/utils/createBot";
import express from "express";
import { format } from "date-fns";
import { UserRecord } from "firebase-admin/lib/auth/user-record";
import { sendConfirmedMeetingEmail } from "@/services/email";
import { MeetingType } from "@/services/email/template/confirmedMeeting";

type UserAidaRequest = {
  // userAidaId
  fUid: string;
  // event id of recall calendar
  eventId: string;
};

export async function userAidaHandler(
  req: express.Request,
  res: express.Response
) {
  try {
    const { fUid, eventId } = req.query as UserAidaRequest;

    await models.UserAida.xUpdate(
      {
        id: fUid,
      },
      {
        isConfirmed: true,
      }
    );

    // schedule bot to the event and session(shouldSendSummaryToEmail: true,) then send email to user
    // fetch event from recall calendar
    const firebaseAuth = await getFirebaseAuth();
    const fUser = await firebaseAuth.getUser(fUid).catch(() => null);
    if (!fUser) {
      res.status(404).send("User not found");
      return;
    }
    // TODO: check why this event fetching is not working
    const event = await recallAiClient.v2_retrieveCalendarEventById(eventId);
    const bot = await scheduleBot(
      event.id,
      event.meeting_url,
      {},
      event.start_time,
      fUser.email
    );
    if (!bot) {
      res.status(404).send("Unable to schedule bot");
      return;
    }
    await models.Session.xCreate({
      recallBotId: bot.bot_id,
      title: `Meeting at ${format(new Date(), "dd MMM yyyy hh:mm a")}`,
      startTime: new Date(event.start_time),
      meetingUrl: event.meeting_url,
      createdById: fUser.uid,
      shouldSendSummaryToEmail: true,
    });

    await sendConfirmationEmail(fUser);

    res.status(201);
  } catch (error) {
    console.error("Error in userAidaHandler:", error);
    res.status(500).send("Internal server error");
  }
}

export const sendConfirmationEmail = async (fUid: UserRecord) => {
  await sendConfirmedMeetingEmail(
    {
      to: fUid.email,
      subject: "Aida is confirmed for your meeting",
      substitutions: {
        username: fUid.displayName || fUid.email,
      },
      attachments: [],
    },
    MeetingType.Confirmed
  );
};
