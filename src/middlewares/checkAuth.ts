import express from "express";
import { log } from "@/services/logger";
import { getFirebaseAuth } from "@/services/firebase";
import { identifyUser } from "@/services/posthog";

// Set to track which users have been identified
const identifiedUsers = new Set<string>();

const checkAuthMiddleware = async (
  req: express.Request,
  res: express.Response,
  next: express.NextFunction
) => {
  if (req.method === "OPTIONS") {
    next();
    return;
  }

  if (req.headers && req.headers.authorization) {
    let token: string = "";
    const parts = req.headers.authorization.split(" ");
    if (parts.length == 2) {
      const [scheme, credentials] = parts;

      if (/^Bearer$/i.test(scheme)) {
        token = credentials;
      }

      if (token === undefined) {
        res.status(401).send("Invalid access token.");
        return;
      }

      try {
        const auth = await getFirebaseAuth();
        const decodedToken = await auth.verifyIdToken(token);
        const user = {
          id: decodedToken.uid,
          email: decodedToken.email,
          name: decodedToken.name,
        };

        res.locals.user = user;

        // Only identify if this user hasn't been identified before
        if (!identifiedUsers.has(user.id)) {
          identifyUser(user.id, {
            email: user.email,
            name: user.name,
          });
          identifiedUsers.add(user.id);
        }

        next();
      } catch (error) {
        log.error(`Failed to verify token: ${error.message}`);
        res.status(401).end("Unauthorized");
      }
    }
  } else {
    res.status(401).send("Authorization header required.");
  }
};

export default checkAuthMiddleware;
