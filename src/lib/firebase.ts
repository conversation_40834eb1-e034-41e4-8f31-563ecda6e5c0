import { getAuth } from 'firebase/auth';
import { initializeApp } from 'firebase/app';
import { getFirestore } from 'firebase/firestore';
import { getAnalytics } from 'firebase/analytics';
import { getAI, VertexAIBackend } from 'firebase/vertexai';

import { CONFIG } from 'src/global-config';

// ----------------------------------------------------------------------

export const firebaseApp = initializeApp(CONFIG.firebase);

export const AUTH = getAuth(firebaseApp);

export const FIRESTORE = getFirestore(firebaseApp);

export const VERTEXAI = getAI(firebaseApp, {
  backend: new VertexAIBackend('global'),
});

export const firebaseAnalytics = getAnalytics(firebaseApp);
