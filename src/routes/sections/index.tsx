import type { RouteObject } from 'react-router';

import { lazy } from 'react';
import { Navigate } from 'react-router';

import { CONFIG } from 'src/global-config';

import { authRoutes } from './auth';
import { dashboardRoutes } from './dashboard';
import { inviteProjectRoutes } from './invite-project';
import { confirmMeetingRoute } from './confirm-meeting';
import { extendRetentionRoutes } from './extend-retention';
import { acceptInvitationRoutes } from './accept-invitation';
// ----------------------------------------------------------------------

const Page404 = lazy(() => import('src/pages/error/404'));

export const routesSection: RouteObject[] = [
  {
    path: '/',
    element: <Navigate to={CONFIG.auth.redirectPath} replace />,
  },

  // Auth
  ...authRoutes,

  // Dashboard
  ...dashboardRoutes,

  // Confirm Meeting
  ...confirmMeetingRoute,

  // Accept Invitation
  ...acceptInvitationRoutes,

  // Invite Project
  ...inviteProjectRoutes,

  // Extend Retention
  ...extendRetentionRoutes,

  // No match
  { path: '*', element: <Page404 /> },
];
