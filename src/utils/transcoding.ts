/**
 * Utility functions for video transcoding
 */

// Function to create transcoding job payload
export const createTranscodingPayload = (inputUri: string, outputUri: string, fileName: string, hasAudio: boolean = true) => {
  // Define types for the elementary streams
  type VideoStream = {
    key: string;
    videoStream: {
      h264: {
        heightPixels: number;
        widthPixels: number;
        bitrateBps: number;
        frameRate: number;
      }
    }
  };
  
  type AudioStream = {
    key: string;
    audioStream: {
      codec: string;
      bitrateBps: number;
    }
  };
  
  type ElementaryStream = VideoStream | AudioStream;
  
  const elementaryStreams: ElementaryStream[] = [
    {
      key: "video-stream0",
      videoStream: {
        h264: {
          heightPixels: 720,
          widthPixels: 1280,
          bitrateBps: 2000000,
          frameRate: 30
        }
      }
    }
  ];
  
  if (hasAudio) {
    elementaryStreams.push({
      key: "audio-stream0",
      audioStream: {
        codec: "aac",
        bitrateBps: 128000
      }
    });
  }
  
  const streamKeys = hasAudio ? ["video-stream0", "audio-stream0"] : ["video-stream0"];
  
  return {
    inputs: [
      {
        key: "input0",
        uri: inputUri
      }
    ],
    output: {
      uri: outputUri
    },
    elementaryStreams,
    muxStreams: [
      {
        key: "sd",
        container: "mp4",
        elementaryStreams: streamKeys,
        fileName: fileName
      }
    ]
  };
}; 
