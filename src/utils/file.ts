import { isArray } from "lodash";
import { extname } from "path";
import { arrToMap } from "./arrays";

export const imageExts = arrToMap([".png", ".jpg", ".jpeg", ".gif", ".svg"]);
export const videoExts = arrToMap([
  ".mp4",
  ".mov",
  ".avi",
  ".flv",
  ".wmv",
  ".m4v",
  ".mpeg",
  ".mpg",
  ".webm",
  ".mkv",
  ".vob",
  ".ogg",
]);
export const audioExts = arrToMap([
  ".wav",
  ".aif",
  ".mp3",
  ".aac",
  ".m4a",
  ".ogg",
  ".flac",
  ".wma",
  ".m4r",
  ".amr",
]);

export const textExts = arrToMap([".txt", ".md", ".pdf"]);

export const uploadExts = {
  ...imageExts,
  ...videoExts,
  ...audioExts,
  ...textExts,
};

export const isVideoOrAudio = (pathOrUrl: string) =>
  isFileType(pathOrUrl, ["video", "audio"]);

export const isTextFile = (pathOrUrl: string) =>
  isFileType(pathOrUrl, ["text"]);

export const isImage = (pathOrUrl: string) => isFileType(pathOrUrl, ["image"]);

export const isFileType = <T extends "video" | "audio" | "image" | "text">(
  pathOrUrl: string,
  type: T | T[]
) => {
  const ext = extname(pathOrUrl).toLowerCase();
  const h = {
    video: videoExts,
    audio: audioExts,
    image: imageExts,
    text: textExts,
  } as { [k: string]: { [k2: string]: boolean } };
  return (
    (!isArray(type) ? h[type]?.[ext] : type.some((t) => h[t]?.[ext])) ?? false
  );
};

export const isVideo = (pathOrUrl: string) => isFileType(pathOrUrl, "video");
