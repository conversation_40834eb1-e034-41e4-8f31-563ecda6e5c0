/* eslint-disable react/no-unescaped-entities */
import { useState, useEffect } from 'react';
import { Helmet } from 'react-helmet-async';

import Box from '@mui/material/Box';
import Card from '@mui/material/Card';
import Alert from '@mui/material/Alert';
import Stack from '@mui/material/Stack';
import Container from '@mui/material/Container';
import Typography from '@mui/material/Typography';
import AlertTitle from '@mui/material/AlertTitle';
import LoadingButton from '@mui/lab/LoadingButton';

import { paths } from 'src/routes/paths';
import { useRouter, useParams, useSearchParams } from 'src/routes/hooks';

import useUserInitialContext from 'src/hooks/user-initial-context';

import { CONFIG } from 'src/global-config';
import { useSharedLinkVerificationMutation } from 'src/store/api/projects';

import { LoadingScreen } from 'src/components/loading-screen';

// ----------------------------------------------------------------------

const metadata = { title: `${CONFIG.appName} - Join Project` };

export default function VerifyProjectSharedLinkPage() {
  useUserInitialContext();

  const router = useRouter();
  const { projectId } = useParams();
  const searchParams = useSearchParams();

  // Get token from URL parameters
  const token = searchParams.get('token');

  const [status, setStatus] = useState<'loading' | 'error' | 'success'>('loading');
  const [errorMessage, setErrorMessage] = useState<string>('');

  const [sharedLinkVerification, { isLoading }] = useSharedLinkVerificationMutation();

  useEffect(() => {
    const handleAcceptInvitation = async () => {
      // Check if required parameters are present
      if (!token || !projectId) {
        setStatus('error');
        setErrorMessage('Invalid shared link. Missing required parameters.');
        return;
      }

      try {
        // Call the API to accept the invitation
        await sharedLinkVerification({
          id: projectId,
          payload: {
            token,
          },
        }).unwrap();

        setStatus('success');
      } catch (error) {
        setStatus('error');
        setErrorMessage(
          'Failed to verify shared link. The shared link may have expired or been revoked.'
        );
        console.error('Error verifying shared link:', error);
      }
    };

    handleAcceptInvitation();
  }, [token, projectId, sharedLinkVerification, router]);

  if (status === 'loading') {
    return <LoadingScreen />;
  }

  return (
    <>
      <Helmet>
        <title>{metadata.title}</title>
      </Helmet>

      <Container maxWidth="sm" sx={{ py: 10 }}>
        <Card sx={{ p: 5 }}>
          <Stack spacing={3} alignItems="center">
            <Typography variant="h4">Project Shared Link Verification</Typography>

            {status === 'success' && (
              <Alert severity="success">
                <AlertTitle>Success</AlertTitle>
                The project owner has been asked to approve your request. You'll receive a response
                soon.
                <Box sx={{ mt: 3, display: 'flex', justifyContent: 'center' }}>
                  <LoadingButton
                    variant="contained"
                    loading={isLoading}
                    onClick={() => router.push(paths.project.root)}
                  >
                    Go to your dashboard
                  </LoadingButton>
                </Box>
              </Alert>
            )}

            {status === 'error' && (
              <Alert severity="error">
                <AlertTitle>Error</AlertTitle>
                {errorMessage}
              </Alert>
            )}

            {status === 'error' && (
              <Box sx={{ mt: 3, display: 'flex', justifyContent: 'center' }}>
                <LoadingButton
                  variant="contained"
                  loading={isLoading}
                  onClick={() => router.push(paths.project.root)}
                >
                  Go to Dashboard
                </LoadingButton>
              </Box>
            )}
          </Stack>
        </Card>
      </Container>
    </>
  );
}
