import { useState, useEffect } from 'react';
import { Helmet } from 'react-helmet-async';

import Box from '@mui/material/Box';
import Card from '@mui/material/Card';
import Alert from '@mui/material/Alert';
import Stack from '@mui/material/Stack';
import Container from '@mui/material/Container';
import Typography from '@mui/material/Typography';
import AlertTitle from '@mui/material/AlertTitle';
import LoadingButton from '@mui/lab/LoadingButton';

import { paths } from 'src/routes/paths';
import { useRouter, useSearchParams } from 'src/routes/hooks';

import { CONFIG } from 'src/global-config';
import { useAcceptInvitationMutation } from 'src/store/api/projects';

import { LoadingScreen } from 'src/components/loading-screen';

// ----------------------------------------------------------------------

const metadata = { title: `${CONFIG.appName} - Accept Project Invitation` };

export default function AcceptProjectInvitationPage() {
  const router = useRouter();
  const searchParams = useSearchParams();

  // Get the invitation code and projectId from URL parameters
  const code = searchParams.get('code');
  const projectId = searchParams.get('projectId');

  const [status, setStatus] = useState<'loading' | 'error' | 'success'>('loading');
  const [errorMessage, setErrorMessage] = useState<string>('');

  const [acceptInvitation, { isLoading }] = useAcceptInvitationMutation();

  useEffect(() => {
    const handleAcceptInvitation = async () => {
      // Check if required parameters are present
      if (!code || !projectId) {
        setStatus('error');
        setErrorMessage('Invalid invitation link. Missing required parameters.');
        return;
      }

      try {
        // Call the API to accept the invitation
        await acceptInvitation({
          id: projectId,
          payload: {
            code,
          },
        }).unwrap();

        setStatus('success');

        // Redirect to project details page after a short delay
        setTimeout(() => {
          router.push(paths.project.details(projectId));
        }, 1000);
      } catch (error) {
        setStatus('error');
        setErrorMessage(
          'Failed to accept invitation. The invitation may have expired or been revoked.'
        );
        console.error('Error accepting invitation:', error);
      }
    };

    handleAcceptInvitation();
  }, [code, projectId, acceptInvitation, router]);

  if (status === 'loading') {
    return <LoadingScreen />;
  }

  return (
    <>
      <Helmet>
        <title>{metadata.title}</title>
      </Helmet>

      <Container maxWidth="sm" sx={{ py: 10 }}>
        <Card sx={{ p: 5 }}>
          <Stack spacing={3} alignItems="center">
            <Typography variant="h4">Project Invitation</Typography>

            {status === 'success' && (
              <Alert severity="success">
                <AlertTitle>Success</AlertTitle>
                You have successfully joined the project. Redirecting you to the project page...
              </Alert>
            )}

            {status === 'error' && (
              <Alert severity="error">
                <AlertTitle>Error</AlertTitle>
                {errorMessage}
              </Alert>
            )}

            {status === 'error' && (
              <Box sx={{ mt: 3, display: 'flex', justifyContent: 'center' }}>
                <LoadingButton
                  variant="contained"
                  loading={isLoading}
                  onClick={() => router.push(paths.project.root)}
                >
                  Go to Projects
                </LoadingButton>
              </Box>
            )}
          </Stack>
        </Card>
      </Container>
    </>
  );
}
