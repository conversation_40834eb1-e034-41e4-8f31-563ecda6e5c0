import cors from "cors";
import express from "express";
import "express-async-errors";

import apiRoutes from "@/api";
import { db } from "@/schemas";
import { log } from "@/services/logger";
import posthog from "@/services/posthog";
import expressErrorHandler from "./handlers/expressErrorHandler";
import { initializeFirebase } from "./services/firebase";
import { initPubSub } from "./services/pubsub";
import { SchedulerService } from "./services/scheduler";

const app = express();

const PORT = process.env.PORT ?? 8080;

app.use(cors());
app.use(express.json({ limit: "50mb" }));
app.use(express.urlencoded({ limit: "50mb", extended: true }));
app.use("/api", apiRoutes);

app.use(expressErrorHandler);

const main = async () => {
  await Promise.all([
    initializeFirebase(),// initialize firebase
    db.authenticate()// check db authentication
  ])
  

  // Sync DB
  // TODO: Remove "alter" option later; keep it here for now for faster development due to many changes with schema
  await db.sync({
    alter: true,
  });

  // Subscribe to PubSub messages
  await initPubSub();

  // Initialize scheduler
  const schedulerService = new SchedulerService();
  schedulerService.start();

  // Start server
  app.listen(PORT, () => {
    console.info(`Successfully started the server on PORT: ${PORT}`);
    console.info(`Environment variables: ${JSON.stringify(process.env)}`);
    // Capture server start event
    posthog.capture({
      distinctId: "server",
      event: "server_started",
      properties: {
        port: PORT,
        environment: process.env.NODE_ENV || "development",
      },
    });
  });
};

main().catch((err) => {
  log.stack(err, "fatal");
  // Capture fatal error event
  posthog.capture({
    distinctId: "server",
    event: "fatal_error",
    properties: {
      error: err.message,
      stack: err.stack,
    },
  });
});
