import { Models } from "@/services/db/Model";
import { db } from "../db";
import ResourceInInsightEngineModel from "../resource/ResourceInInsightEngine.model";

export type TranscriptionModel = typeof TranscriptionModel;
export type Transcription = TranscriptionModel["$M"];
export type TranscriptionCol = TranscriptionModel["$K"];
export type TranscriptionCreate = TranscriptionModel["$C"];
export type TranscriptionUpdate = TranscriptionModel["$U"];
export type TranscriptionWhere = TranscriptionModel["$W"];

const TranscriptionModel = db.xDefine("Transcription", {
  content: {
    type: "TEXT",
    defaultValue: "",
  },
  resourceInInsightEngineId: {
    type: "STRING",
  },
  nameFromRevAi: {
    type: "STRING",
    defaultValue: "Unknown name",
  },
  startTime: {
    type: "FLOAT",
    defaultValue: 0,
  },
  endTime: {
    type: "FLOAT",
    defaultValue: 0,
  },
  speakerId: {
    type: "STRING",
    // can be null in seeding
    // or to lazy clean up after association deleted
    allowNull: true,
  },
});

// Associations
TranscriptionModel.hasOne(ResourceInInsightEngineModel, {
  sourceKey: "resourceInInsightEngineId",
  foreignKey: "id",
  constraints: false,
});

export default TranscriptionModel;
