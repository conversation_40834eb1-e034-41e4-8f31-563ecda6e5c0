import { generateSignedUrlForRead } from "@/services/storage";
import { ffprobe } from "@/services/upload/ffprobe";
import { isVideoOrAudio } from "@/utils";
import { submitJob as submitTranscriptionJob } from "@/services/transcription";
import models from "../models";
import { upsertUserStatistics } from "../statistics/utils";
import { IEUploadAction } from "../insightEngine/InsightEngine.model";
import { TranscriptStatus } from "./ResourceInInsightEngine.model";
import { log } from "@/services/logger";
import {
  Resource,
  ResourceCreate,
  ResourceModel,
  ResourceType,
} from "./Resource.model";
import { getInsightEngineByResourceId } from "../insightEngine/utils";
import { ResourceData } from "@/types/resources";

interface FFProbeResult {
  duration: number;
  thumbnailUrl: string | null;
  [key: string]: any;
}

export const createNewResource = async ({
  gcsFilePath,
  fileName,
  fileSize = 0,
  fileLastModified = new Date(),
  userId,
  title,
  uploadAction,
  sessionId,
  projectId,
  folderId,
  transcodedUrl,
  isTranscoding
}: {
  gcsFilePath: string;
  fileName: string;
  fileSize?: number;
  fileLastModified?: Date;
  userId: string;
  title: string;
  uploadAction: IEUploadAction;
  sessionId?: string;
  projectId?: string;
  folderId?: string;
  transcodedUrl?: string;
  isTranscoding?: boolean;
}) => {
  const resourceSignedUrl = await generateSignedUrlForRead(gcsFilePath);

  log.debug("[createNewResource] Start processing resource", resourceSignedUrl);

  const resourcePayload: ResourceCreate = {
    fileSize,
    name: fileName,
    createdById: userId,
    fileLastModified,
    url: gcsFilePath,
    type: ResourceType.resource,
    sessionId,
    projectId,
    folderId,
    transcodedUrl,
    isTranscoding
  };

  if (isVideoOrAudio(fileName)) {
    log.debug("[createNewResource] Processing video or audio file:", fileName);
    try {
      const ffprobeResult = (await Promise.race([
        ffprobe(
          resourceSignedUrl,
          fileName,
          isVideoOrAudio(fileName)
        ) as Promise<FFProbeResult>,
        new Promise<never>((_, reject) =>
          setTimeout(() => reject(new Error("ffprobe timeout")), 30000)
        ),
      ])) as FFProbeResult;

      resourcePayload.duration = ffprobeResult.duration;
      resourcePayload.ffprobe = ffprobeResult;
      resourcePayload.thumbnailUrl = ffprobeResult.thumbnailUrl;
    } catch (error) {
      log.error("[createNewResource] Error processing media file:", error);
      // Continue with default values if ffprobe fails
      resourcePayload.duration = 0;
      resourcePayload.ffprobe = {};
      resourcePayload.thumbnailUrl = null;
    }
  }

  const resource = await models.Resource.xCreate(resourcePayload);
  log.debug("[createNewResource] Created resource", resource);

  try {
    await upsertUserStatistics(userId, {
      totalResourcesDuration: resourcePayload.duration
        ? Number(resourcePayload.duration)
        : 0,
      totalResourcesCount: 1,
      totalFilesSize: fileSize,
    });
    log.debug("[createNewResource] Upserted user statistics");
  } catch (error) {
    log.error("[createNewResource] Error updating user statistics:", error);
    // Continue even if statistics update fails
  }

  const insightEngine = await models.InsightEngine.xCreate({
    name: title,
    createdById: userId,
    uploadAction,
  });
  log.debug("[createNewResource] Created insight engine", insightEngine);

  const resourceInIE = await models.ResourceInInsightEngine.xCreate({
    insightEngineId: insightEngine.id,
    resourceId: resource.id,
  });
  log.debug(
    "[createNewResource] Created resource in insight engine",
    resourceInIE
  );

  if (isVideoOrAudio(fileName)) {
    const job = await submitTranscriptionJob(resourceSignedUrl);
    log.debug("[createNewResource] Submitted transcription job", job);

    await models.ResourceInInsightEngine.xUpdateById(resourceInIE.id, {
      revAiJobId: job.id,
      status: TranscriptStatus.InProgress,
    });
  } else {
    await models.ResourceInInsightEngine.xUpdateById(resourceInIE.id, {
      status: TranscriptStatus.Completed,
    });
  }
  log.debug(
    "[createNewResource] Updated resource in insight engine",
    resourceInIE
  );

  return { resource, insightEngine, resourceInIE };
};

export const getResourceData = async (
  resource: Resource
): Promise<ResourceData> => {
  const { insightEngine, resourceInInsightEngine: riie } =
    await getInsightEngineByResourceId(resource.id);

  const transcription = await models.Transcription.xFindBy(
    "resourceInInsightEngineId",
    riie?.id
  );

  const publicUrl = await generateSignedUrlForRead(resource.url);
  const thumbnailPublicUrl = await generateSignedUrlForRead(
    resource.thumbnailUrl
  );

  return {
    ...resource,
    fileName: resource.name,
    name: insightEngine?.name ?? resource.name,
    url: publicUrl,
    thumbnailUrl: thumbnailPublicUrl,
    fileLastModified: resource.fileLastModified ?? resource.createdAt,
    transcription,
    transcriptionJobStatus: riie?.status,
  };
};
