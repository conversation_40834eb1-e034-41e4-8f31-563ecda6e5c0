import { ts } from "@/types";
import { db } from "../db";
import { Models } from "@/services/db/Model";
import ResourceModel from "./Resource.model";
import InsightEngineModel from "../insightEngine/InsightEngine.model";

export type ResourceInInsightEngineModel = typeof ResourceInInsightEngineModel;

export type ResourceInInsightEngine = ResourceInInsightEngineModel["$M"];
export type ResourceInInsightEngineCol = ResourceInInsightEngineModel["$K"];
export type ResourceInInsightEngineCreate = ResourceInInsightEngineModel["$C"];
export type ResourceInInsightEngineUpdate = ResourceInInsightEngineModel["$U"];
export type ResourceInInsightEngineWhere = ResourceInInsightEngineModel["$W"];

export enum TranscriptStatus {
  UnTranscript = "UnTranscript",
  InProgress = "InProgress",
  Failed = "Failed",
  Completed = "Completed",
}
const ResourceInInsightEngineModel = db.xDefine("ResourceInInsightEngine", {
  insightEngineId: {
    type: "STRING",
  },
  resourceId: {
    type: "STRING",
  },
  note: {
    type: "TEXT",
    defaultValue: "",
  },
  revAiJobId: {
    type: "STRING",
    allowNull: true,
  },
  revAiExtractionJobId: {
    type: "STRING",
    allowNull: true,
  },
  isFromBot: {
    type: "BOOLEAN",
    defaultValue: false,
  },
  isDownloading: {
    type: "BOOLEAN",
    defaultValue: false,
  },
  // Transcription status
  status: {
    type: "STRING",
    tsType: ts<TranscriptStatus>(),
    defaultValue: TranscriptStatus.UnTranscript,
  },
});

// Associations
ResourceInInsightEngineModel.hasOne(ResourceModel, {
  sourceKey: "resourceId",
  foreignKey: "id",
  constraints: false,
});

ResourceInInsightEngineModel.hasOne(InsightEngineModel, {
  sourceKey: "insightEngineId",
  foreignKey: "id",
  constraints: false,
});

export default ResourceInInsightEngineModel;
