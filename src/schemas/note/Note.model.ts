import { Models } from "@/services/db/Model";
import { db } from "../db";
import ResourceModel from "../resource/Resource.model";
import ProjectModel from "../project/Project.model";

export type NoteModel = typeof NoteModel;
export type Note = NoteModel["$M"];
export type NoteCol = NoteModel["$K"];
export type NoteCreate = NoteModel["$C"];
export type NoteUpdate = NoteModel["$U"];
export type NoteWhere = NoteModel["$W"];

const NoteModel = db.xDefine("Note", {
  title: {
    type: "STRING",
    defaultValue: "",
  },
  content: {
    type: "TEXT",
    defaultValue: "",
  },
  resourceId: {
    type: "STRING",
    allowNull: true,
  },
  projectId: {
    type: "STRING",
    allowNull: true,
  },
  createdById: {
    type: "STRING",
  },
  updatedById: {
    type: "STRING",
  },
});

// Associations
NoteModel.hasOne(ResourceModel, {
  sourceKey: "resourceId",
  foreignKey: "id",
  constraints: false,
  as: "resource",
});

NoteModel.hasOne(ProjectModel, {
  sourceKey: "projectId",
  foreignKey: "id",
  constraints: false,
  as: "project",
});

export default NoteModel;
