import { BotChangeStatusCode } from "@/types/recall";
import models from "../models";
import { Session } from "./Session.model";

export const updateSessionByBotId = (botId: string, data: Partial<Session>) => {
  return models.Session.xUpdateBy("recallBotId", botId, data);
};

export const isBotNotJoiningCallTimeout = async (botId: string) => {
  const session = await models.Session.xFind1({
      recallBotId: botId
  });
  return session?.recallBotStatus === BotChangeStatusCode.TimeoutExceededWaitingRoom;
};
