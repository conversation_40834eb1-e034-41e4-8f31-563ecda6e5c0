import { db } from "../db";
import ResourceModel from "../resource/Resource.model";
import SessionModel from "../session/Session.model";
import ProjectFolderModel from "./ProjectFolder.model";

export type ProjectModel = typeof ProjectModel;
export type Project = ProjectModel["$M"];
export type ProjectCol = ProjectModel["$K"];
export type ProjectCreate = ProjectModel["$C"];
export type ProjectUpdate = ProjectModel["$U"];
export type ProjectWhere = ProjectModel["$W"];

const ProjectModel = db.xDefine("Project", {
  name: {
    type: "STRING",
  },
  description: {
    type: "TEXT",
    allowNull: true,
  },
  createdById: {
    type: "STRING",
    allowNull: true,
  },
  // For soft delete
  isDeleted: {
    type: "BOOLEAN",
    defaultValue: false,
  },
  // List of user ids that are allowed to access the project
  sharedWith: {
    type: "JSON",
    allowNull: true,
  },

  isDefault: {
    type: "BOOLEAN",
    defaultValue: false,
  },
});

// Association with Project Folders
ProjectModel.hasMany(ProjectFolderModel, {
  foreignKey: "projectId",
  constraints: false,
  as: "folders",
});

// Association with Resources
ProjectModel.hasMany(ResourceModel, {
  foreignKey: "projectId",
  constraints: false,
  as: "resources",
});

// Association with Sessions
ProjectModel.hasMany(SessionModel, {
  foreignKey: "projectId",
  constraints: false,
  as: "sessions",
});

ResourceModel.belongsTo(ProjectModel, {
  foreignKey: "projectId",
  constraints: false,
  as: "project",
});

export default ProjectModel;
