import { db } from "../db";
import ProjectModel from "./Project.model";
import { ProjectMemberRole } from "./ProjectMember.model";

export enum InvitationStatus {
    SENT = "SENT",
    APPROVED = "APPROVED",
    REJECTED = "REJECTED"
}

export type ProjectInvitationModelType = typeof ProjectInvitationModel;
export type ProjectInvitation = ProjectInvitationModelType["$M"];
export type ProjectInvitationCol = ProjectInvitationModelType["$K"];
export type ProjectInvitationCreate = ProjectInvitationModelType["$C"];
export type ProjectInvitationUpdate = ProjectInvitationModelType["$U"];
export type ProjectInvitationWhere = ProjectInvitationModelType["$W"];

const ProjectInvitationModel = db.xDefine("ProjectInvitation", {
    status: {
        type: "STRING",
        values: Object.values(InvitationStatus),
        defaultValue: InvitationStatus.SENT,
        allowNull: false,
    },
    code: {
        type: "STRING",
        allowNull: false,
        unique: true,
    },
    email: {
        type: "STRING",
        allowNull: false,
    },
    projectId: {
        type: "STRING",
        allowNull: false,
    },
    role: {
        type: "STRING",
        values: Object.values(ProjectMemberRole),
        defaultValue: ProjectMemberRole.VIEWER,
        allowNull: false,
    },
    expiredAt: {
        type: "DATE",
        allowNull: false,
    },
    userId: {
        type: "STRING",
        allowNull: true,
    },
});

// Association with Project
ProjectInvitationModel.belongsTo(ProjectModel, {
    foreignKey: "projectId",
    constraints: false,
    as: "project",
});

// Add association to Project model
ProjectModel.hasMany(ProjectInvitationModel, {
    foreignKey: "projectId",
    constraints: false,
    as: "invitations",
});

export default ProjectInvitationModel; 