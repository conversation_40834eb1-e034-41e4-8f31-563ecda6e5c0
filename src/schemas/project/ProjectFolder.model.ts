import { db } from "../db";
import ResourceModel from "../resource/Resource.model";

export type ProjectFolderModel = typeof ProjectFolderModel;
export type ProjectFolder = ProjectFolderModel["$M"];
export type ProjectFolderCol = ProjectFolderModel["$K"];
export type ProjectFolderCreate = ProjectFolderModel["$C"];
export type ProjectFolderUpdate = ProjectFolderModel["$U"];
export type ProjectFolderWhere = ProjectFolderModel["$W"];

const ProjectFolderModel = db.xDefine("ProjectFolder", {
  name: {
    type: "STRING",
  },
  description: {
    type: "TEXT",
    allowNull: true,
  },
  projectId: {
    type: "STRING",
  },
  parentId: {
    type: "STRING",
    allowNull: true,
  },
  createdById: {
    type: "STRING",
    allowNull: true,
  },
  // For soft delete
  isDeleted: {
    type: "BOOLEAN",
    defaultValue: false,
  },
});

ResourceModel.belongsTo(ProjectFolderModel, {
  foreignKey: "folderId",
  constraints: false,
  as: "folder",
});

export default ProjectFolderModel;
