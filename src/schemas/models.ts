import InsightEngineModel from "./insightEngine/InsightEngine.model";
import ResourceModel from "./resource/Resource.model";
import ResourceInInsightEngineModel from "./resource/ResourceInInsightEngine.model";
import UserStatisticsModel from "./statistics/UserStatistics.model";
import TranscriptionModel from "./transcription/Transcription.model";
import SessionModel from "./session/Session.model";
import ProjectModel from "./project/Project.model";
import ProjectFolderModel from "./project/ProjectFolder.model";
import NoteModel from "./note/Note.model";
import UserAidaModel from "./userAida/UserAida.model";
import SummaryModel from "./summary/Summary.model";
import ProjectInvitationModel from "./project/ProjectInvitation.model";
import ProjectMemberModel from "./project/ProjectMember.model";
import { ProjectAccessRequestModel } from "./project/ProjectAccessRequest.model";
import RetentionSettingModel from "./retention/RetentionSetting.model";
import UserFeedbackModel from "@/schemas/UserFeedback/UserFeedback.model";
import ResourceTranscodedJobModel from "@/schemas/transcoded/ResourceTranscodedJob.model";

const models = {
  Resource: ResourceModel,
  ResourceInInsightEngine: ResourceInInsightEngineModel,
  InsightEngine: InsightEngineModel,
  Transcription: TranscriptionModel,
  UserStatistics: UserStatisticsModel,
  Session: SessionModel,
  Project: ProjectModel,
  ProjectFolder: ProjectFolderModel,
  Note: NoteModel,
  UserAida: UserAidaModel,
  Summary: SummaryModel,
  ProjectAccessRequest: ProjectAccessRequestModel,
  ProjectInvitation: ProjectInvitationModel,
  ProjectMember: ProjectMemberModel,
  RetentionSetting: RetentionSettingModel,
  UserFeedback: UserFeedbackModel,
  ResourceTranscodedJob: ResourceTranscodedJobModel,
};

export default models;
