import type { Resource, ResourcePermission } from './resource';

export interface Project {
  id: string;
  name: string;
  description: string;
  createdAt: Date;
  updatedAt: Date;
  totalFilesSize: number;
  totalFilesCount: number;
  userPermissions?: ResourcePermission;
  createdById: string;
  isDefault?: boolean;
}

export interface ProjectFolder {
  id: string;
  name: string;
  createdAt: Date;
  updatedAt: Date;
  projectId: string;
  createdById: string;
}

export interface ProjectUser {
  uid: string;
  displayName: string;
  email: string;
  photoURL: string;
  providerId: string;
}

export interface ProjectDetails extends Project {
  folders: ProjectFolder[];
  resources: Resource[];
  createdById: string;
}

export interface ProjectMember {
  id: string;
  projectId: string;
  userId: string;
  role: 'VIEWER' | 'EDITOR';
  createdAt: Date;
  user: ProjectUser;
}

export interface PendingInvitation {
  status: 'SENT';
  code: string;
  email: string;
  projectId: string;
  role: 'VIEWER' | 'EDITOR';
  expiredAt: Date;
  userId: string | null;
  id: string;
  createdAt: Date;
}

export interface PublishedShareLink {
  token: string;
  projectId: string;
  expiredAt: Date;
  maxAccessCount: number;
}

export interface AccessRequest {
  projectId: string;
  sharedLinkId: string;
  requestedById: string;
  status: 'PENDING';
  approvedAt: Date | null;
  rejectedAt: Date | null;
  rejectedReason: string | null;
  expiredAt: Date;
  id: string;
  createdAt: Date;
  user: ProjectUser;
}
