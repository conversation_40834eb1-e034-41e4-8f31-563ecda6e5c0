import { TranscriptStatus } from "@/schemas/resource/ResourceInInsightEngine.model";
import { Transcription } from "@/schemas/transcription/Transcription.model";

export enum ResourceUploadAction {
  UPLOAD = "upload",
  RECORD = "record",
}

export interface ResourceData {
  id: string;
  duration: number;
  thumbnailUrl: string;
  fileSize: number;
  fileName: string;
  name: string;
  createdById: string;
  url: string;
  createdAt: Date;
  fileLastModified: Date;
  orgId?: string;
  sessionId?: string;
  transcription?: Transcription[];
  transcriptionJobStatus?: TranscriptStatus;
}
