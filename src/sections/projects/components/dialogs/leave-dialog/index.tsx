import type { Project } from 'src/types/project';

import { toast } from 'sonner';
import { useDispatch } from 'react-redux';

import LoadingButton from '@mui/lab/LoadingButton';
import {
  Box,
  Dialog,
  Button,
  Typography,
  DialogTitle,
  DialogContent,
  DialogActions,
  DialogContentText,
} from '@mui/material';

import { useRouter } from 'src/routes/hooks';

import { useAppSelector } from 'src/store';
import { useLeaveProjectMutation } from 'src/store/api/projects';
import { setLastViewedProjectId } from 'src/store/slices/settings/slice';
import { selectLastViewedProjectId } from 'src/store/slices/settings/selectors';

import { Iconify } from 'src/components/iconify';

interface Props {
  open: boolean;
  onClose: () => void;
  project: Pick<Project, 'id' | 'name'>;
}

const LeaveProjectDialog: React.FC<Props> = ({ open, onClose, project }) => {
  const dispatch = useDispatch();
  const router = useRouter();
  const currentProjectId = useAppSelector(selectLastViewedProjectId);
  const [triggerLeaveProject, { isLoading }] = useLeaveProjectMutation();

  const handleLeave = async () => {
    try {
      await triggerLeaveProject({ id: project.id }).unwrap();
      onClose();

      // If the current project is the one being left, redirect to the home page
      if (currentProjectId === project.id) {
        router.push('/');
        dispatch(setLastViewedProjectId(null));
      }

      toast.success('You have successfully left the project');
    } catch (error) {
      console.error('Failed to leave project:', error);
      toast.error('Failed to leave project. Please try again.');
    }
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      onClick={(evt) => evt.stopPropagation()}
      maxWidth="sm"
      fullWidth
    >
      <DialogTitle sx={{ display: 'flex', alignItems: 'center', gap: 1, color: 'warning.main' }}>
        <Iconify icon="solar:logout-2-bold" sx={{ color: 'warning.main' }} />
        Leave Project
      </DialogTitle>

      <DialogContent>
        <DialogContentText component="div">
          <Typography variant="body1" sx={{ mb: 2 }}>
            Are you sure you want to leave &quot;<strong>{project.name}</strong>&quot;?
          </Typography>

          <Box sx={{ bgcolor: 'warning.lighter', p: 2, borderRadius: 1, mb: 2 }}>
            <Typography variant="subtitle2" sx={{ color: 'warning.dark', mb: 1 }}>
              ⚠️ Important: What happens when you leave
            </Typography>
            <Box component="ul" sx={{ m: 0, pl: 2, color: 'warning.dark' }}>
              <li>You will lose access to all project files and resources</li>
              <li>You will no longer receive project notifications</li>
              <li>You cannot undo this action unless re-invited by a project owner</li>
              <li>Any ongoing work or sessions will be interrupted</li>
            </Box>
          </Box>

          <Typography variant="body2" color="text.secondary">
            If you only need temporary access restrictions, consider asking a project owner to
            change your role instead of leaving completely.
          </Typography>
        </DialogContentText>
      </DialogContent>

      <DialogActions sx={{ p: 3, pt: 1 }}>
        <Button onClick={onClose} variant="outlined" color="inherit">
          Cancel
        </Button>
        <LoadingButton
          onClick={handleLeave}
          color="warning"
          variant="contained"
          loading={isLoading}
          disabled={isLoading}
          startIcon={<Iconify icon="solar:logout-2-bold" />}
        >
          Leave Project
        </LoadingButton>
      </DialogActions>
    </Dialog>
  );
};

export default LeaveProjectDialog;
