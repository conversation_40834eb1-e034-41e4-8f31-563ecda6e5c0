import { useBoolean } from 'minimal-shared/hooks';
import { useRef, useMemo, useState, useEffect, useCallback } from 'react';

import { useParams } from 'src/routes/hooks';

import {
  ProjectRole,
  RequestStatus,
  projectsService,
  useInviteUserMutation,
  usePublishShareLinkMutation,
  useGetListProjectMembersQuery,
  useGetListAccessRequestsQuery,
  useRemoveProjectMemberMutation,
  useApproveOrRejectAccessMutation,
  useGetListPendingInvitationsQuery,
  useChangeRoleOfProjectMemberMutation,
} from 'src/store/api/projects';

import { toast } from 'src/components/snackbar';

import { InviteContext } from './invite-context';

import type { InviteProviderProps } from '../types';
// ----------------------------------------------------------------------

export function InviteProvider({ children }: InviteProviderProps) {
  const { value: open, onTrue: onOpen, onFalse: onClose } = useBoolean();
  const { id: projectId } = useParams();
  const oldProjectId = useRef(projectId);
  const openedByDefaultCount = useRef(0);

  // Loading states for individual actions
  const [isApprovingOrRejecting, setIsApprovingOrRejecting] = useState<Record<string, string>>({});
  const [isRemoving, setIsRemoving] = useState<Record<string, boolean>>({});

  const [inviteUserMutation] = useInviteUserMutation();
  const [changeUserRoleMutation] = useChangeRoleOfProjectMemberMutation();
  const [publishShareLinkMutation] = usePublishShareLinkMutation();
  const [approveOrRejectAccessMutation] = useApproveOrRejectAccessMutation();
  const [removeProjectMemberMutation] = useRemoveProjectMemberMutation();

  // Reset states when project changes
  useEffect(() => {
    setIsApprovingOrRejecting({});
    setIsRemoving({});

    // Reset only specific queries data
    if (projectId) {
      projectsService.util.updateQueryData('getListProjectMembers', { id: projectId }, () => []);
      projectsService.util.updateQueryData(
        'getListPendingInvitations',
        { id: projectId, query: { status: 'SENT' } },
        () => []
      );
      projectsService.util.updateQueryData(
        'getListAccessRequests',
        { id: projectId, query: { status: 'PENDING' } },
        () => []
      );
    }
  }, [projectId]);

  const {
    data: projectMembers,
    isLoading: isMembersLoading,
    isFetching: isMembersFetching,
  } = useGetListProjectMembersQuery(
    {
      id: projectId!,
    },
    {
      skip: !projectId,
      refetchOnFocus: true,
      refetchOnMountOrArgChange: true,
    }
  );

  const {
    data: pendingInvitations,
    isLoading: isInvitationsLoading,
    isFetching: isInvitationsFetching,
  } = useGetListPendingInvitationsQuery(
    {
      id: projectId!,
      query: {
        status: 'SENT',
      },
    },
    {
      skip: !projectId,
      refetchOnFocus: true,
      refetchOnMountOrArgChange: true,
    }
  );

  const {
    data: accessRequests,
    isLoading: isAccessRequestsLoading,
    isFetching: isAccessRequestsFetching,
  } = useGetListAccessRequestsQuery(
    {
      id: projectId!,
      query: {
        status: 'PENDING',
      },
    },
    {
      skip: !projectId,
      refetchOnFocus: true,
      refetchOnMountOrArgChange: true,
    }
  );

  useEffect(() => {
    if (window.location.hash === '#sharing' && !open) {
      onOpen();
      window.location.hash = '';
    }
  }, [open, onOpen, window.location.hash]);

  // Combine loading states and handle project change
  const isLoading = useMemo(() => {
    if (!projectId || !open) return false;
    if (
      oldProjectId.current !== projectId &&
      (isMembersFetching || isInvitationsFetching || isAccessRequestsFetching)
    ) {
      oldProjectId.current = projectId;
      return isMembersFetching || isInvitationsFetching || isAccessRequestsFetching;
    }
    return isMembersLoading || isInvitationsLoading || isAccessRequestsLoading;
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    open,
    isMembersLoading,
    isInvitationsLoading,
    isAccessRequestsLoading,
    isMembersFetching,
    isInvitationsFetching,
    isAccessRequestsFetching,
  ]);

  const inviteUser = useCallback(
    async (email: string) => {
      if (!projectId) {
        toast.error('Cannot invite users without a project');
        return;
      }

      try {
        await inviteUserMutation({
          id: projectId,
          payload: {
            email,
            role: ProjectRole.VIEWER, // Default role for new invitees
          },
          query: {
            force: true,
          },
        }).unwrap();

        toast.success(`Invitation sent to ${email}`);
      } catch (error) {
        toast.error('Failed to send invitation');
        console.error(error);
      }
    },
    [projectId, inviteUserMutation]
  );

  // Change user role
  const changeUserRole = useCallback(
    async (userId: string, role: ProjectRole) => {
      if (!projectId) {
        toast.error('Cannot change user role without a project');
        return;
      }

      try {
        await changeUserRoleMutation({
          id: projectId,
          memberId: userId,
          payload: { role },
        }).unwrap();

        toast.success('User role changed');
      } catch (error) {
        toast.error('Failed to change user role');
        console.error(error);
      }
    },
    [projectId, changeUserRoleMutation]
  );

  const removeMember = useCallback(
    async (userId: string) => {
      if (!projectId) {
        toast.error('Cannot remove user without a project');
        return;
      }

      try {
        setIsRemoving((prev) => ({ ...prev, [userId]: true }));

        await removeProjectMemberMutation({
          id: projectId,
          memberId: userId,
        }).unwrap();

        toast.success('User removed from project');
      } catch (error) {
        toast.error('Failed to remove user');
        console.error(error);
      } finally {
        setIsRemoving((prev) => ({ ...prev, [userId]: false }));
      }
    },
    [projectId, removeProjectMemberMutation]
  );

  const approveAccessRequest = useCallback(
    async (accessRequestId: string, status: RequestStatus) => {
      if (!projectId) {
        toast.error('Cannot approve access request without a project');
        return;
      }

      try {
        setIsApprovingOrRejecting((prev) => ({ ...prev, [accessRequestId]: status }));

        const isApproved = status === RequestStatus.APPROVED;

        await approveOrRejectAccessMutation({
          id: projectId,
          accessRequestId,
          payload: { isApproved },
        }).unwrap();

        toast.success(isApproved ? 'Access request approved' : 'Access request rejected');
      } catch (error) {
        toast.error('Failed to process access request');
        console.error(error);
      } finally {
        // remove the status from the record
        setIsApprovingOrRejecting((prev) => {
          const newState = { ...prev };
          delete newState[accessRequestId];
          return newState;
        });
      }
    },
    [projectId, approveOrRejectAccessMutation]
  );

  const copyInviteLink = useCallback(async () => {
    if (!projectId) {
      toast.error('Cannot copy invite link without a project');
      return;
    }

    const shareLink = await publishShareLinkMutation({
      id: projectId!,
    }).unwrap();

    const link = `${window.location.origin}/invite-project/${projectId || 'default'}?token=${shareLink.token}`;

    navigator.clipboard
      .writeText(link)
      .then(() => {
        toast.success('Link copied to clipboard');
      })
      .catch(() => {
        toast.error('Failed to copy link');
      });
  }, [projectId, publishShareLinkMutation]);

  const contextValue = useMemo(
    () => ({
      open,
      onOpen,
      onClose,
      users: projectMembers || [],
      pendingInvitations: pendingInvitations || [],
      accessRequests: accessRequests || [],
      isLoading,
      isApprovingOrRejecting,
      isRemoving,
      inviteUser,
      changeUserRole,
      copyInviteLink,
      approveAccessRequest,
      removeMember,
    }),
    [
      open,
      onOpen,
      onClose,
      projectMembers,
      pendingInvitations,
      accessRequests,
      isLoading,
      isApprovingOrRejecting,
      isRemoving,
      inviteUser,
      changeUserRole,
      copyInviteLink,
      approveAccessRequest,
      removeMember,
    ]
  );

  return <InviteContext.Provider value={contextValue}>{children}</InviteContext.Provider>;
}
