import {
  List,
  Avatar,
  Divider,
  ListItem,
  Typography,
  ListItemText,
  ListItemAvatar,
} from '@mui/material';

import { getRoleDisplay } from '../utils';

import type { InvitationsListProps } from './types';

// ----------------------------------------------------------------------

export function InvitationsList({
  data,
  title,
  loading,
  showDivider = false,
}: InvitationsListProps) {
  if (loading) {
    return null;
  }

  if (data.length === 0) {
    return null;
  }

  return (
    <>
      {showDivider && <Divider />}
      {title && (
        <Typography variant="subtitle2" sx={{ mt: 1 }}>
          {title}
        </Typography>
      )}
      <List
        sx={{
          width: '100%',
          maxHeight: '300px',
          overflow: 'auto',
        }}
      >
        {data.map((invitation) => (
          <ListItem
            key={invitation.id}
            secondaryAction={
              <Typography
                variant="body2"
                noWrap
                sx={{ color: 'text.secondary', textTransform: 'capitalize' }}
              >
                {invitation.status.toLowerCase()}
              </Typography>
            }
          >
            <ListItemAvatar>
              <Avatar alt={invitation.email} src={invitation.email}>
                {invitation.email.charAt(0).toUpperCase()}
              </Avatar>
            </ListItemAvatar>
            <ListItemText primary={invitation.email} secondary={getRoleDisplay(invitation.role)} />
          </ListItem>
        ))}
      </List>
    </>
  );
}
