import type { ReactNode } from 'react';
import type { ProjectRole, RequestStatus } from 'src/store/api/projects';
import type { ProjectMember, AccessRequest, PendingInvitation } from 'src/types/project';

// ----------------------------------------------------------------------

export interface InviteDialogProps {
  title?: string;
}

export interface InviteContextValue {
  // Dialog state
  open: boolean;
  onOpen: () => void;
  onClose: () => void;

  // Users state
  users: ProjectMember[];
  pendingInvitations: PendingInvitation[];
  accessRequests: AccessRequest[];
  isLoading: boolean;

  // Loading states
  isApprovingOrRejecting: Record<string, string>;
  isRemoving: Record<string, boolean>;

  // Actions
  inviteUser: (email: string) => Promise<void>;
  changeUserRole: (userId: string, role: ProjectRole) => Promise<void>;
  copyInviteLink: () => void;
  approveAccessRequest: (accessRequestId: string, status: RequestStatus) => Promise<void>;
  removeMember?: (userId: string) => Promise<void>;
}

export interface InviteProviderProps {
  children: ReactNode;
}
