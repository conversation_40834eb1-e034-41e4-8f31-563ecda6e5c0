import type { UserProject } from 'src/types/user';

import { useMemo, useCallback } from 'react';
import { useBoolean, usePopover } from 'minimal-shared/hooks';

import { Toolt<PERSON>, Divider, MenuItem, MenuList, IconButton } from '@mui/material';

import { Iconify } from 'src/components/iconify';
import { CustomPopover } from 'src/components/custom-popover';

import LeaveProjectDialog from '../dialogs/leave-dialog';
import RenameProjectDialog from '../dialogs/rename-dialog';
import RemoveProjectDialog from '../dialogs/remove-dialog';

const ProjectItemActions: React.FC<{ data: UserProject }> = ({ data }) => {
  const menuActions = usePopover();
  const renameProjectDialog = useBoolean();
  const removeProjectDialog = useBoolean();
  const leaveProjectDialog = useBoolean();

  const isOwner = data.isOwner;
  const isDefault = data.isDefault;

  console.log('LLLL', {
    isOwner,
    isDefault,
    data,
  });

  const isHasActions = useMemo(() => {
    const canRename = isOwner && !isDefault;
    const canDelete = isOwner && !isDefault;
    const canLeave = !isOwner;

    return canRename || canDelete || canLeave;
  }, [isOwner, isDefault]);

  const renderRenameProjectMenuItem = useCallback(() => {
    if (isDefault || !isOwner) return null;
    return (
      <>
        <MenuItem
          onClick={(evt) => {
            evt.stopPropagation();
            renameProjectDialog.onTrue();
            menuActions.onClose();
          }}
        >
          <Iconify icon="eva:edit-2-outline" />
          Rename
        </MenuItem>
        <Divider />
      </>
    );
  }, [isDefault, isOwner, menuActions, renameProjectDialog]);

  const renderDeleteProjectMenuItem = useCallback(() => {
    if (isDefault || !isOwner) return null;

    return (
      <MenuItem
        onClick={(evt) => {
          evt.stopPropagation();
          removeProjectDialog.onTrue();
          menuActions.onClose();
        }}
        sx={{ color: 'error.main' }}
      >
        <Iconify icon="solar:trash-bin-trash-bold" />
        Delete
      </MenuItem>
    );
  }, [isDefault, isOwner, menuActions, removeProjectDialog]);

  const renderLeaveProjectMenuItem = useCallback(() => {
    if (isOwner) return null;
    return (
      <MenuItem
        onClick={(evt) => {
          evt.stopPropagation();
          leaveProjectDialog.onTrue();
          menuActions.onClose();
        }}
        sx={{ color: 'warning.main' }}
      >
        <Iconify icon="solar:logout-2-bold" />
        Leave Project
      </MenuItem>
    );
  }, [isOwner, menuActions, leaveProjectDialog]);

  if (!isHasActions) return null;

  return (
    <>
      <Tooltip title="More actions" placement="top" arrow>
        <IconButton
          disableRipple
          sx={{ py: 0 }}
          onClick={(evt) => {
            evt.stopPropagation();
            menuActions.onOpen(evt);
          }}
        >
          <Iconify icon="eva:more-vertical-fill" />
        </IconButton>
      </Tooltip>

      <CustomPopover
        open={menuActions.open}
        anchorEl={menuActions.anchorEl}
        onClose={(evt, reason) => {
          menuActions.onClose();
        }}
        slotProps={{ arrow: { placement: 'top-left' } }}
      >
        <MenuList>
          {renderRenameProjectMenuItem()}
          {renderDeleteProjectMenuItem()}
          {renderLeaveProjectMenuItem()}
        </MenuList>
      </CustomPopover>

      <RenameProjectDialog
        open={renameProjectDialog.value}
        onClose={renameProjectDialog.onFalse}
        project={data}
      />

      <RemoveProjectDialog
        open={removeProjectDialog.value}
        onClose={removeProjectDialog.onFalse}
        project={data}
      />

      <LeaveProjectDialog
        open={leaveProjectDialog.value}
        onClose={leaveProjectDialog.onFalse}
        project={data}
      />
    </>
  );
};

export default ProjectItemActions;
