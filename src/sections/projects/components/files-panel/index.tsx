import type { Resource } from 'src/types';
import type { SxProps } from '@mui/material';

import { useSelector } from 'react-redux';

import { List, Paper, Stack, Checkbox, Typography } from '@mui/material';

import useUserSessions from 'src/hooks/user-sessions';

import { useGetProjectDetailsQuery } from 'src/store/api/projects';
import { selectPendingResourceUploadsForProject } from 'src/store/slices/resources/selectors';

import { Scrollbar } from 'src/components/scrollbar';
import { EmptyContent } from 'src/components/empty-content';
import { LoadingScreen } from 'src/components/loading-screen';

import FileCard from './file-card';
import SessionCard from './session-card';
import PendingUploadCard from './pending-upload-card';

interface FilesPanelProps {
  selectedFiles: Resource[];
  onFilesSelected?: (files: Resource[]) => void;
  projectId: string;
  sx?: SxProps;
}

const FilesPanel = ({ selectedFiles, onFilesSelected, projectId, sx = {} }: FilesPanelProps) => {
  const { ongoingSessions = [] } = useUserSessions(projectId);
  const selectedFileIds = selectedFiles.map((f) => f.id);

  const {
    data: project,
    isLoading,
    isFetching,
  } = useGetProjectDetailsQuery(
    { id: projectId },
    {
      skip: !projectId,
      refetchOnFocus: false,
    }
  );

  const pendingUploads = useSelector((state) =>
    selectPendingResourceUploadsForProject(state, projectId)
  );

  const { resources = [] } = project ?? {};
  const sortedResources = [...resources].sort(
    (a, b) => new Date(b.fileLastModified).getTime() - new Date(a.fileLastModified).getTime()
  );

  const isEmpty =
    sortedResources.length === 0 && ongoingSessions.length === 0 && pendingUploads.length === 0;

  const handleSelectFile = (file: Resource, isSelected: boolean) => {
    if (isSelected) {
      onFilesSelected?.(selectedFiles.concat(file));
    } else {
      onFilesSelected?.(selectedFiles.filter((f) => f.id !== file.id));
    }
  };

  return (
    <Paper
      elevation={1}
      sx={{
        overflow: 'hidden',
        display: 'flex',
        flexDirection: 'column',
        p: 2,
        width: {
          xs: '100%',
          md: '25%',
        },
        height: ['50%', '100%'],
        ...sx,
      }}
    >
      {isLoading || isFetching || !project ? (
        <LoadingScreen />
      ) : (
        <>
          <Stack direction="row" alignItems="center" justifyContent="space-between">
            <Typography variant="subtitle2">Total Files: {resources.length}</Typography>
            <Checkbox
              size="small"
              checked={selectedFiles.length === resources.length && resources.length > 0}
              indeterminate={selectedFiles.length > 0 && selectedFiles.length < resources.length}
              onChange={(e) => {
                if (e.target.checked) {
                  onFilesSelected?.(resources);
                } else {
                  onFilesSelected?.([]);
                }
              }}
            />
          </Stack>

          <Scrollbar
            sx={{
              height: '100%',
              width: '100%',
            }}
          >
            <List
              dense
              sx={{
                flex: 1,
                overflowY: 'auto',
                height: '100%',
                gap: 1,
                display: 'flex',
                flexDirection: 'column',
              }}
            >
              {isEmpty ? (
                <EmptyContent
                  title="No files available"
                  description="Upload file or start recording to get started"
                  sx={{
                    height: '100%',
                  }}
                />
              ) : (
                <>
                  {/* Pending Uploads */}
                  {pendingUploads.map((upload) => (
                    <PendingUploadCard key={upload.id} data={upload} />
                  ))}

                  {/* On-going Sessions */}
                  {ongoingSessions.map((session) => (
                    <SessionCard key={session.id} session={session} />
                  ))}

                  {/* Files */}
                  {sortedResources.map((file) => (
                    <FileCard
                      key={file.id}
                      file={file}
                      onSelect={handleSelectFile}
                      isSelected={selectedFileIds.includes(file.id)}
                    />
                  ))}
                </>
              )}
            </List>
          </Scrollbar>
        </>
      )}
    </Paper>
  );
};

export default FilesPanel;
