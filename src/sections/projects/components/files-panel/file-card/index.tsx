import type { Resource } from 'src/types';
import type { SxProps } from '@mui/material';
import type { ResourceItem } from 'src/sections/resources/components/resources-list';

import { useState } from 'react';
import { format } from 'date-fns';

import VideocamIcon from '@mui/icons-material/Videocam';
import InsertDriveFileRoundedIcon from '@mui/icons-material/InsertDriveFileRounded';
import { Box, Card, Stack, Collapse, Checkbox, IconButton, Typography } from '@mui/material';

import { Iconify } from 'src/components/iconify';
import { fileFormat } from 'src/components/file-thumbnail';
import TruncateTypography from 'src/components/truncate-typography';

import ResourceActions from 'src/sections/resources/components/resource-actions';
import ResourceThumbnail from 'src/sections/resources/components/resource-card/components/resource-thumbnail';
import ResourcePreviewer from 'src/sections/resources/components/resource-card/components/resource-previewer';

interface FileCardProps {
  file: ResourceItem;
  sx?: SxProps;
  onSelect: (file: Resource, isSelected: boolean) => void;
  isSelected: boolean;
}

const FileCard = ({ file, sx, onSelect, isSelected }: FileCardProps) => {
  const [expanded, setExpanded] = useState(false);

  const handleExpandClick = () => {
    setExpanded(!expanded);
  };

  const handleCheckboxChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    onSelect(file, event.target.checked);
  };

  const fFormat = fileFormat(file.fileName || file.name || '');
  const isAudioOrVideo = fFormat === 'audio' || fFormat === 'video';

  return (
    <Card
      sx={{
        width: '100%',
        bgcolor: 'background.paper',
        borderRadius: 1,
        border: '1px solid',
        borderColor: 'divider',
        boxShadow: 'none',
        ...sx,
      }}
    >
      <Stack
        direction="row"
        alignItems="center"
        spacing={2}
        sx={{
          p: 1,
        }}
      >
        {isAudioOrVideo ? (
          <VideocamIcon color="action" sx={{ flexShrink: 0 }} />
        ) : (
          <InsertDriveFileRoundedIcon color="action" sx={{ flexShrink: 0 }} />
        )}

        <Stack sx={{ flexGrow: 1, minWidth: 0 }}>
          <TruncateTypography
            text={file.name}
            variant="subtitle1"
            fontSize={14}
            sx={{
              whiteSpace: 'nowrap',
            }}
          />
          <Typography variant="caption" color="textSecondary">
            {format(file.fileLastModified, 'dd MMM yyyy hh:mm a')}
          </Typography>
        </Stack>

        <Stack direction="row" alignItems="center" sx={{ flexShrink: 0 }}>
          <Checkbox checked={isSelected} onChange={handleCheckboxChange} size="small" />
          <IconButton onClick={handleExpandClick} size="small">
            <Iconify
              icon={
                expanded
                  ? 'material-symbols:collapse-content-rounded'
                  : 'material-symbols:expand-content-rounded'
              }
              sx={{
                transition: 'transform 0.2s',
              }}
            />
          </IconButton>
          <ResourceActions resource={file} hiddenActions={['select']} />
        </Stack>
      </Stack>

      <Collapse in={expanded} timeout="auto" unmountOnExit>
        <Box sx={{ p: 1 }}>
          {isAudioOrVideo ? <ResourcePreviewer data={file} /> : <ResourceThumbnail data={file} />}
        </Box>
      </Collapse>
    </Card>
  );
};

export default FileCard;
