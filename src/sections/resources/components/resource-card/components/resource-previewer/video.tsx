import type { Resource } from 'src/types';

import { useRef } from 'react';
import { toast } from 'sonner';

import { CardCover } from '@mui/joy';
import LoadingButton from '@mui/lab/LoadingButton';
import { Card, Tooltip, useTheme, useMediaQuery } from '@mui/material';

import { useUploadResourceThumbnailMutation } from 'src/store/api/resources';

import { Image } from 'src/components/image';

const ResourceVideoPreviewer: React.FC<{ data: Resource }> = ({ data }) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const [uploadResourceThumbnail, { isLoading }] = useUploadResourceThumbnailMutation();
  const videoRef = useRef<HTMLVideoElement>(null);

  const generateThumbnail = () => {
    if (!videoRef.current) return null;

    // Pause video to capture frame
    videoRef.current.pause();

    // <PERSON>le generate thumbnail from frame
    const canvas = document.createElement('canvas');
    canvas.width = videoRef.current.videoWidth;
    canvas.height = videoRef.current.videoHeight;

    const ctx = canvas.getContext('2d');
    if (!ctx) return null;

    ctx.drawImage(videoRef.current, 0, 0, canvas.width, canvas.height);
    const imageUrl = canvas.toDataURL('image/png');

    return imageUrl;
  };

  const handleCaptureFrame = async () => {
    const imageUrl = generateThumbnail();
    if (!imageUrl) return;

    try {
      await uploadResourceThumbnail({ id: data.id, payload: { imageUrl } }).unwrap();
      toast.success('Thumbnail uploaded successfully');
    } catch (error) {
      toast.error('Failed to upload thumbnail');
    }
  };

  if (!data.url) {
    return (
      <Image
        src={data.thumbnailUrl}
        alt={data.thumbnailUrl}
        ratio="auto"
        sx={{ borderRadius: 1, height: isMobile ? '150px' : '250px' }}
      />
    );
  }

  return (
    <Card
      sx={{
        minWidth: '100%',
        height: [200, 250],
        flexGrow: 1,
      }}
    >
      <Tooltip arrow title="Choose this frame as resource thumbnail">
        <LoadingButton
          loading={isLoading}
          variant="contained"
          size="small"
          color="primary"
          sx={{ zIndex: 10, ml: 2, mt: 2 }}
          onClick={handleCaptureFrame}
        >
          Capture as thumbnail
        </LoadingButton>
      </Tooltip>
      <CardCover>
        <video ref={videoRef} controls poster={data.thumbnailUrl} crossOrigin="anonymous">
          <source src={data.url} type="video/mp4" />
        </video>
      </CardCover>
    </Card>
  );
};

export default ResourceVideoPreviewer;
