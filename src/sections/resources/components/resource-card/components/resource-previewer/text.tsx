import { useState, useEffect } from 'react';

import { Markdown } from 'src/components/markdown';
import { Scrollbar } from 'src/components/scrollbar';
import { LoadingScreen } from 'src/components/loading-screen';

import type { ResourceItem } from '../../../resources-list';

const ResourceTextPreviewer: React.FC<{ data: ResourceItem }> = ({ data }) => {
  const [texts, setTexts] = useState<string>('');

  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (!data.url) return;
    setLoading(true);
    fetch(data.url)
      .then((response) => response.text())
      .then((text) => {
        setTexts(text);
      })
      .finally(() => setLoading(false));
  }, [data.url]);

  if (loading) return <LoadingScreen />;

  return (
    <Scrollbar
      sx={{
        height: 500,
      }}
    >
      <Markdown
        sx={{
          whiteSpace: 'pre-wrap',
          wordWrap: 'break-word',
          overflowWrap: 'break-word',
        }}
        children={texts}
      />
    </Scrollbar>
  );
};

export default ResourceTextPreviewer;
