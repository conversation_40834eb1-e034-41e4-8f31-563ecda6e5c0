import 'react-pdf/dist/esm/Page/AnnotationLayer.css';
import 'react-pdf/dist/esm/Page/TextLayer.css';

import type { Resource } from 'src/types';

import { useRef, useState } from 'react';
import { useResizeObserver } from 'usehooks-ts';
import { Page, pdfjs, Document } from 'react-pdf';

import { Box, Fade } from '@mui/material';

import { LoadingScreen } from 'src/components/loading-screen';

pdfjs.GlobalWorkerOptions.workerSrc = new URL(
  'pdfjs-dist/build/pdf.worker.min.mjs',
  import.meta.url
).toString();

const options = {
  cMapUrl: '/cmaps/',
  standardFontDataUrl: '/standard_fonts/',
};

const ResourcePdfPreviewer: React.FC<{
  data: Resource;
  maxWidth?: number;
}> = ({ data, maxWidth = 600 }) => {
  const [numPages, setNumPages] = useState<number>(0);
  const [isLoading, setIsLoading] = useState(true);
  const containerRef = useRef<HTMLDivElement>(null);
  const [containerWidth, setContainerWidth] = useState<number>();

  useResizeObserver({
    ref: containerRef,
    onResize: (size) => setContainerWidth(size.width),
  });

  const handleLoadSuccess = ({ numPages: nextNumPages }: { numPages: number }) => {
    setNumPages(nextNumPages);
    setIsLoading(false);
  };

  return (
    <Box
      className="scrollbar"
      ref={containerRef}
      sx={{
        overflow: 'auto',
        height: '100vh',
        display: 'flex',
        justifyContent: 'center',
        bgcolor: 'background.paper',
        position: 'relative',
      }}
    >
      {isLoading && (
        <Box
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            bgcolor: 'background.paper',
            zIndex: 1,
          }}
        >
          <LoadingScreen />
        </Box>
      )}
      <Fade in={!isLoading} timeout={500}>
        <Box>
          <Document
            loading={null}
            file={data.url}
            onLoadSuccess={handleLoadSuccess}
            options={options}
          >
            {Array.from(new Array(numPages), (_, index) => (
              <Box key={`page_${index + 1}`} sx={{ py: 2 }}>
                <Page
                  pageNumber={index + 1}
                  width={containerWidth ? Math.min(containerWidth, maxWidth) : maxWidth}
                  renderTextLayer
                  renderAnnotationLayer
                  loading={null}
                />
              </Box>
            ))}
          </Document>
        </Box>
      </Fade>
    </Box>
  );
};

export default ResourcePdfPreviewer;
