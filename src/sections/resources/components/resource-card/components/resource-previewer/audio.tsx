import type { Resource } from 'src/types';

import { Stack } from '@mui/material';

const ResourceAudioPreviewer: React.FC<{ data: Resource }> = ({ data }) => (
  <Stack direction="row" justifyContent="center" alignItems="center" sx={{ height: 100 }}>
    <audio controls crossOrigin="anonymous" style={{ width: '100%' }}>
      <source src={data.url} type="audio/mp3" />
    </audio>
  </Stack>
);

export default ResourceAudioPreviewer;
