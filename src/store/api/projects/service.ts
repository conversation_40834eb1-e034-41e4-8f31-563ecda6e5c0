import type {
  Project,
  ProjectMember,
  AccessRequest,
  ProjectDetails,
  PendingInvitation,
  PublishedShareLink,
} from 'src/types/project';

import { apiService } from '..';
import { projectApiConfigs } from './configs';

import type { BaseResponse } from '../types';
import type { ProjectConfigParams } from './configs';
import type {
  CreateProjectRequest,
  UpdateProjectRequest,
  GetProjectFolderDetailsResponse,
} from './types';

export const projectsService = apiService.injectEndpoints({
  endpoints: (build) => ({
    getProjects: build.query<Project[], {}>({
      query: projectApiConfigs.getProjects,
      providesTags: (result) =>
        result
          ? [
              ...result.map(({ id }) => ({ type: 'Projects' as const, id })),
              { type: 'Projects', id: 'LIST' },
            ]
          : [{ type: 'Projects', id: 'LIST' }],
    }),
    getProjectDetails: build.query<ProjectDetails, ProjectConfigParams<'getProjectDetails'>>({
      query: projectApiConfigs.getProjectDetails,
      transformResponse: (response: BaseResponse<ProjectDetails>) => response.data,
      providesTags: (result) =>
        result
          ? [
              { type: 'Projects' as const, id: result.id },
              ...result.folders.map(({ id }) => ({ type: 'ProjectFolders' as const, id })),
            ]
          : [],
    }),
    createProject: build.mutation<CreateProjectRequest, ProjectConfigParams<'createProject'>>({
      query: projectApiConfigs.createProject,
      invalidatesTags: ['Projects', 'InitialContext'],
    }),
    updateProject: build.mutation<UpdateProjectRequest, ProjectConfigParams<'updateProject'>>({
      query: projectApiConfigs.updateProject,
      invalidatesTags: (result, error, arg) => [
        { type: 'Projects' as const, id: arg.id },
        'InitialContext',
      ],
    }),
    deleteProject: build.mutation<void, ProjectConfigParams<'deleteProject'>>({
      query: projectApiConfigs.deleteProject,
      async onQueryStarted(arg, { dispatch, queryFulfilled }) {
        const patchResult = dispatch(
          projectsService.util.updateQueryData('getProjects', {}, (draft) =>
            draft.filter((item) => item.id !== arg.id)
          )
        );
        try {
          await queryFulfilled;
        } catch {
          patchResult.undo();
        }
      },
      invalidatesTags: ['Projects', 'InitialContext'],
    }),
    createProjectFolder: build.mutation<void, ProjectConfigParams<'createProjectFolder'>>({
      query: projectApiConfigs.createProjectFolder,
      invalidatesTags: (result, error, arg) => [{ type: 'Projects', id: arg.id }, 'InitialContext'],
    }),
    deleteProjectFolder: build.mutation<void, ProjectConfigParams<'deleteProjectFolder'>>({
      query: projectApiConfigs.deleteProjectFolder,
      async onQueryStarted(arg, { dispatch, queryFulfilled }) {
        const patchResult = dispatch(
          projectsService.util.updateQueryData('getProjectDetails', { id: arg.id }, (draft) => {
            if (draft.folders) {
              draft.folders = draft.folders.filter((folder) => folder.id !== arg.folderId);
            }
          })
        );

        try {
          await queryFulfilled;
        } catch {
          patchResult.undo();
        }
      },
    }),
    getProjectFolderDetails: build.query<
      GetProjectFolderDetailsResponse,
      ProjectConfigParams<'getProjectFolderDetails'>
    >({
      query: projectApiConfigs.getProjectFolderDetails,
      providesTags: (result) =>
        result ? [{ type: 'ProjectFolders' as const, id: result.folder.id }] : [],
    }),
    inviteUser: build.mutation<void, ProjectConfigParams<'inviteUser'>>({
      query: projectApiConfigs.inviteUser,
      async onQueryStarted(arg, { dispatch, queryFulfilled }) {
        try {
          await queryFulfilled;

          // Invalidate pending invitations list to refetch
          dispatch(
            projectsService.util.invalidateTags([
              { type: 'PendingInvitations', id: arg.id },
              { type: 'PendingInvitations', id: 'LIST' },
            ])
          );
        } catch {
          // Error handling
        }
      },
    }),
    acceptInvitation: build.mutation<void, ProjectConfigParams<'acceptInvitation'>>({
      query: projectApiConfigs.acceptInvitation,
      invalidatesTags: (result, error, arg) => [{ type: 'Projects', id: arg.id }],
    }),
    getListPendingInvitations: build.query<
      PendingInvitation[],
      ProjectConfigParams<'getListPendingInvitations'>
    >({
      query: projectApiConfigs.getListPendingInvitations,
      transformResponse: (response: BaseResponse<PendingInvitation[]>) => response.data,
      providesTags: (result, error, { id }) => [
        { type: 'PendingInvitations' as const, id },
        { type: 'PendingInvitations', id: 'LIST' },
      ],
    }),
    getListProjectMembers: build.query<
      ProjectMember[],
      ProjectConfigParams<'getListProjectMembers'>
    >({
      query: projectApiConfigs.getListProjectMembers,
      transformResponse: (response: BaseResponse<ProjectMember[]>) => response.data,
      providesTags: (result, error, { id }) => {
        const tags: Array<{ type: 'ProjectMembers'; id: string }> = [
          { type: 'ProjectMembers', id: 'LIST' },
          { type: 'ProjectMembers', id },
        ];

        if (result) {
          result.forEach((member) => {
            tags.push({ type: 'ProjectMembers', id: member.id });
          });
        }

        return tags;
      },
    }),
    getProjectMembership: build.query<ProjectMember, ProjectConfigParams<'getProjectMembership'>>({
      query: projectApiConfigs.getProjectMembership,
      transformResponse: (response: BaseResponse<ProjectMember>) => response.data,
      providesTags: (result, error, { id }) => [
        { type: 'ProjectMembers' as const, id },
        { type: 'ProjectMembers', id: 'LIST' },
      ],
    }),
    changeRoleOfProjectMember: build.mutation<
      void,
      ProjectConfigParams<'changeRoleOfProjectMember'>
    >({
      query: projectApiConfigs.changeRoleOfProjectMember,
      invalidatesTags: (result, error, arg) => [{ type: 'ProjectMembers', id: arg.id }],
    }),
    publishShareLink: build.mutation<PublishedShareLink, ProjectConfigParams<'publishShareLink'>>({
      query: projectApiConfigs.publishShareLink,
      transformResponse: (response: BaseResponse<PublishedShareLink>) => response.data,
      invalidatesTags: (result, error, arg) => [{ type: 'PublishedShareLinks', id: arg.id }],
    }),
    sharedLinkVerification: build.mutation<void, ProjectConfigParams<'sharedLinkVerification'>>({
      query: projectApiConfigs.sharedLinkVerification,
      invalidatesTags: (result, error, arg) => [{ type: 'AccessRequests', id: arg.id }],
    }),
    getListAccessRequests: build.query<
      AccessRequest[],
      ProjectConfigParams<'getListAccessRequests'>
    >({
      query: projectApiConfigs.getListAccessRequests,
      transformResponse: (response: BaseResponse<AccessRequest[]>) => response.data,
      providesTags: (result, error, { id }) => [
        { type: 'AccessRequests' as const, id },
        { type: 'ProjectMembers', id: 'LIST' },
      ],
    }),
    approveOrRejectAccess: build.mutation<void, ProjectConfigParams<'approveOrRejectAccess'>>({
      query: projectApiConfigs.approveOrRejectAccess,
      invalidatesTags: (result, error, arg) => [
        { type: 'AccessRequests', id: arg.id },
        { type: 'ProjectMembers', id: 'LIST' },
      ],
    }),
    removeProjectMember: build.mutation<void, ProjectConfigParams<'removeProjectMember'>>({
      query: projectApiConfigs.removeProjectMember,
      invalidatesTags: (result, error, arg) => [
        { type: 'ProjectMembers', id: arg.id },
        { type: 'ProjectMembers', id: 'LIST' },
      ],
    }),
    leaveProject: build.mutation<void, ProjectConfigParams<'leaveProject'>>({
      query: projectApiConfigs.leaveProject,
      invalidatesTags: (result, error, arg) => [
        'Projects',
        'InitialContext',
        { type: 'ProjectMembers', id: arg.id },
        { type: 'ProjectMembers', id: 'LIST' },
      ],
    }),
    createProjectDefault: build.mutation<void, ProjectConfigParams<'createProjectDefault'>>({
      query: projectApiConfigs.createProjectDefault,
      invalidatesTags: (result, error, arg) => [{ type: 'Projects', id: 'LIST' }],
    }),
  }),
  overrideExisting: true,
});
