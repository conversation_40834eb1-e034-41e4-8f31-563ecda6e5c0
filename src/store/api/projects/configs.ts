import type { ApiRequestConfig } from 'src/store/api/types';

import type {
  InviteUserQuery,
  InviteUserRequest,
  CreateProjectRequest,
  UpdateProjectRequest,
  AcceptInvitationRequest,
  CreateProjectFolderRequest,
  ApproveOrRejectAccessRequest,
  SharedLinkVerificationRequest,
  ChangeRoleOfProjectMemberRequest,
} from './types';

export interface ProjectApiConfigs {
  getProjects: ApiRequestConfig<{}>;
  createProject: ApiRequestConfig<{
    payload: CreateProjectRequest;
  }>;
  deleteProject: ApiRequestConfig<{
    id: string;
  }>;
  getProjectDetails: ApiRequestConfig<{
    id: string;
    filter?: {
      includeAllResources?: boolean;
    };
  }>;
  createProjectFolder: ApiRequestConfig<{
    id: string;
    payload: CreateProjectFolderRequest;
  }>;
  updateProject: ApiRequestConfig<{
    id: string;
    payload: UpdateProjectRequest;
  }>;
  deleteProjectFolder: ApiRequestConfig<{
    id: string;
    folderId: string;
  }>;
  getProjectFolderDetails: ApiRequestConfig<{
    id: string;
    folderId: string;
  }>;
  inviteUser: ApiRequestConfig<{
    id: string;
    query: InviteUserQuery;
    payload: InviteUserRequest;
  }>;
  acceptInvitation: ApiRequestConfig<{
    id: string;
    payload: AcceptInvitationRequest;
  }>;
  getListPendingInvitations: ApiRequestConfig<{
    id: string;
    query: {
      status: 'SENT';
    };
  }>;
  getListProjectMembers: ApiRequestConfig<{
    id: string;
  }>;
  changeRoleOfProjectMember: ApiRequestConfig<{
    id: string;
    memberId: string;
    payload: ChangeRoleOfProjectMemberRequest;
  }>;
  publishShareLink: ApiRequestConfig<{
    id: string;
  }>;
  sharedLinkVerification: ApiRequestConfig<{
    id: string;
    payload: SharedLinkVerificationRequest;
  }>;
  getListAccessRequests: ApiRequestConfig<{
    id: string;
    query: {
      status: 'PENDING';
    };
  }>;
  approveOrRejectAccess: ApiRequestConfig<{
    id: string;
    accessRequestId: string;
    payload: ApproveOrRejectAccessRequest;
  }>;
  getProjectMembership: ApiRequestConfig<{
    id: string;
  }>;
  removeProjectMember: ApiRequestConfig<{
    id: string;
    memberId: string;
  }>;
  leaveProject: ApiRequestConfig<{
    id: string;
  }>;
  createProjectDefault: ApiRequestConfig<{}>;
}

export type ProjectConfigParams<Config extends keyof ProjectApiConfigs> = Parameters<
  ProjectApiConfigs[Config]
>[0];

export const projectApiConfigs: ProjectApiConfigs = {
  getProjects: () => ({
    method: 'GET',
    uri: 'projects',
  }),
  createProject: (args) => ({
    method: 'POST',
    uri: 'project',
    data: args.payload,
  }),
  updateProject: (args) => ({
    method: 'PUT',
    uri: `project/${args.id}`,
    data: args.payload,
  }),
  deleteProject: (args) => ({
    method: 'DELETE',
    uri: `project/${args.id}`,
  }),
  getProjectDetails: (args) => ({
    method: 'GET',
    uri: `project/${args.id}`,
    params: args.filter,
  }),
  createProjectFolder: (args) => ({
    method: 'POST',
    uri: `project/${args.id}/folder`,
    data: args.payload,
  }),
  deleteProjectFolder: (args) => ({
    method: 'DELETE',
    uri: `project/${args.id}/folder/${args.folderId}`,
  }),
  getProjectFolderDetails: (args) => ({
    method: 'GET',
    uri: `project/${args.id}/folder/${args.folderId}`,
  }),
  inviteUser: (args) => ({
    method: 'POST',
    uri: `project/invite/${args.id}`,
    data: args.payload,
    params: args.query,
  }),
  acceptInvitation: (args) => ({
    method: 'POST',
    uri: `project/invitation/accept/${args.id}`,
    data: args.payload,
  }),
  getListPendingInvitations: (args) => ({
    method: 'GET',
    uri: `project/invitations/${args.id}`,
    params: args.query,
  }),
  getListProjectMembers: (args) => ({
    method: 'GET',
    uri: `project/members/${args.id}`,
  }),
  changeRoleOfProjectMember: (args) => ({
    method: 'PUT',
    uri: `project/member/change-role/${args.id}/${args.memberId}`,
    data: args.payload,
  }),
  getProjectMembership: (args) => ({
    method: 'GET',
    uri: `project/member/me/${args.id}`,
  }),
  publishShareLink: (args) => ({
    method: 'POST',
    uri: `project/shared-link/${args.id}`,
  }),
  getListAccessRequests: (args) => ({
    method: 'GET',
    uri: `project/access-request/${args.id}`,
    params: args.query,
  }),
  approveOrRejectAccess: (args) => ({
    method: 'PUT',
    uri: `project/access-request/response/${args.id}/${args.accessRequestId}`,
    data: args.payload,
  }),
  sharedLinkVerification: (args) => ({
    method: 'POST',
    uri: `project/shared-link/verify/${args.id}`,
    data: args.payload,
  }),
  removeProjectMember: (args) => ({
    method: 'DELETE',
    uri: `project/member/${args.id}/${args.memberId}`,
  }),
  leaveProject: (args) => ({
    method: 'DELETE',
    uri: `project/${args.id}/members/leave`,
  }),
  createProjectDefault: (args) => ({
    method: 'POST',
    uri: `project/project-default`,
  }),
};
