import { projectsService } from './service';

export const {
  useGetProjectsQuery,
  useCreateProjectMutation,
  useDeleteProjectMutation,
  useLazyGetProjectDetailsQuery,
  useGetProjectDetailsQuery,
  useCreateProjectFolderMutation,
  useDeleteProjectFolderMutation,
  useGetProjectFolderDetailsQuery,
  useGetListPendingInvitationsQuery,
  useGetListProjectMembersQuery,
  useGetListAccessRequestsQuery,
  useInviteUserMutation,
  useAcceptInvitationMutation,
  useChangeRoleOfProjectMemberMutation,
  usePublishShareLinkMutation,
  useApproveOrRejectAccessMutation,
  useSharedLinkVerificationMutation,
  useGetProjectMembershipQuery,
  useRemoveProjectMemberMutation,
  useLeaveProjectMutation,
  useCreateProjectDefaultMutation,
  useUpdateProjectMutation,
} = projectsService;
