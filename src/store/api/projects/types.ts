import type { Resource } from 'src/types';
import type { Project, ProjectFolder } from 'src/types/project';

export enum ProjectRole {
  VIEWER = 'VIEWER',
  EDITOR = 'EDITOR',
}

export enum RequestStatus {
  PENDING = 'PENDING',
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED',
}

export interface CreateProjectRequest {
  name: string;
  description?: string;
}

export type UpdateProjectRequest = Partial<Project>;

export interface CreateProjectFolderRequest {
  name: string;
}

export interface GetProjectFolderDetailsResponse {
  folder: ProjectFolder;
  resources: Resource[];
}

export interface InviteUserQuery {
  force?: boolean;
}

export interface InviteUserRequest {
  email: string;
  role: ProjectRole;
}

export interface AcceptInvitationRequest {
  code: string;
}

export interface ChangeRoleOfProjectMemberRequest {
  role: ProjectRole;
}

export interface ApproveOrRejectAccessRequest {
  isApproved: boolean;
}

export interface SharedLinkVerificationRequest {
  token: string;
}

export interface GetProjectMembershipRequest {
  id: string;
}
