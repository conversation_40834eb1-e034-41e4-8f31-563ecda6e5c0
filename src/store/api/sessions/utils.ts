import type { Draft } from 'immer';
import type { Session } from 'src/types';
import type { ThunkDispatch } from '@reduxjs/toolkit';

import { sessionsService } from './service';

export const optimisticAddSessionToList = (sessions: Draft<Session[]>, newSession: Session) => {
  // Add to the beginning of the list (most recent first)
  sessions.unshift(newSession);
};

export const optimisticUpdateSessionInList = (
  sessions: Draft<Session[]>,
  updatedSession: Session
) => {
  const index = sessions.findIndex((session) => session.id === updatedSession.id);
  if (index !== -1) {
    sessions[index] = updatedSession;
  }
};

export const optimisticRemoveSessionFromList = (sessions: Draft<Session[]>, sessionId: string) => {
  const index = sessions.findIndex((session) => session.id === sessionId);
  if (index !== -1) {
    sessions.splice(index, 1);
  }
};

export const optimisticAddSession = (
  dispatch: ThunkDispatch<any, any, any>,
  newSession: Session,
  projectId?: string
) => {
  // Add to global sessions list
  dispatch(
    sessionsService.util.updateQueryData('getSessions', {}, (draft) =>
      optimisticAddSessionToList(draft, newSession)
    )
  );

  // Add to ongoing sessions if applicable
  if (projectId) {
    dispatch(
      sessionsService.util.updateQueryData('getOngoingSessions', { projectId }, (draft) =>
        optimisticAddSessionToList(draft, newSession)
      )
    );
  }

  // Also add to ongoing sessions without projectId filter
  dispatch(
    sessionsService.util.updateQueryData('getOngoingSessions', {}, (draft) =>
      optimisticAddSessionToList(draft, newSession)
    )
  );
};

export const optimisticUpdateSession = (
  dispatch: ThunkDispatch<any, any, any>,
  updatedSession: Session
) => {
  // Update in global sessions list
  dispatch(
    sessionsService.util.updateQueryData('getSessions', {}, (draft) =>
      optimisticUpdateSessionInList(draft, updatedSession)
    )
  );

  // Update in ongoing sessions
  dispatch(
    sessionsService.util.updateQueryData('getOngoingSessions', {}, (draft) =>
      optimisticUpdateSessionInList(draft, updatedSession)
    )
  );

  // Update individual session cache
  dispatch(
    sessionsService.util.updateQueryData(
      'getSession',
      { id: updatedSession.id },
      () => updatedSession
    )
  );
};

export const optimisticRemoveSession = (
  dispatch: ThunkDispatch<any, any, any>,
  sessionId: string,
  getState: () => any
) => {
  const patchResults = [];

  // Remove from global sessions list
  const globalPatch = dispatch(
    sessionsService.util.updateQueryData('getSessions', {}, (draft) =>
      optimisticRemoveSessionFromList(draft, sessionId)
    )
  );
  patchResults.push(globalPatch);

  // Remove from ongoing sessions if present
  const state = getState();
  const queries = state.api.queries;

  Object.keys(queries).forEach((queryKey) => {
    if (queryKey.startsWith('getOngoingSessions(')) {
      const query = queries[queryKey];
      if (query?.data?.some((session: any) => session.id === sessionId)) {
        // Extract projectId from query key if present
        const projectMatch = queryKey.match(/projectId":"([^"]+)"/);
        const projectId = projectMatch ? projectMatch[1] : undefined;

        const patch = dispatch(
          sessionsService.util.updateQueryData(
            'getOngoingSessions',
            projectId ? { projectId } : {},
            (draft) => optimisticRemoveSessionFromList(draft, sessionId)
          )
        );
        patchResults.push(patch);
      }
    }
  });

  return patchResults;
};
