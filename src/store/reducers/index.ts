import type { AnyAction } from '@reduxjs/toolkit';

import { combineReducers } from '@reduxjs/toolkit';

import { notesSlice } from 'src/store/slices/notes/slice';
import { notesInitialState } from 'src/store/slices/notes/types';
import { settingsSlice, settingsInitialState } from 'src/store/slices/settings/slice';
import { resourcesSlice, resourcesInitialState } from 'src/store/slices/resources/slice';

import { apiService } from '../api';

export type RootState = ReturnType<typeof reducers>;

export type ApplicationActions = AnyAction;

const reducers = combineReducers({
  resources: resourcesSlice.reducer,
  notes: notesSlice.reducer,
  settings: settingsSlice.reducer,
  [apiService.reducerPath]: apiService.reducer,
});

export const defaultState: RootState = {
  resources: resourcesInitialState,
  notes: notesInitialState,
  settings: settingsInitialState,
  [apiService.reducerPath]: {} as ReturnType<typeof apiService.reducer>,
};

const rootReducer = (state: RootState = defaultState, action: ApplicationActions) =>
  reducers(state, action);

export default rootReducer;
